EXPO_PUBLIC_SUPABASE_URL=https://zlivouxymzpbyoxnwxrp.supabase.co
EXPO_PUBLIC_SUPABASE_KEY=sb_publishable_ABgFmonyDTQFCL9IiD9Pzw_H7-dr7yC

# Note: Google OAuth is handled via Supabase OAuth configuration
# No client ID needed in app config since we use Supabase's OAuth flow

OPENROUTER_API_KEY=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
OPENROUTER_MODEL=google/gemini-2.5-flash-lite

# Cloudflare R2 Configuration (currently not in use, but required for app.config.js)
#CLOUDFLARE_ACCOUNT_ID=placeholder
#R2_ACCESS_KEY_ID=placeholder
#R2_SECRET_ACCESS_KEY=placeholder
#R2_BUCKET_NAME=placeholder
#R2_ENDPOINT_URL=https://placeholder.r2.cloudflarestorage.com
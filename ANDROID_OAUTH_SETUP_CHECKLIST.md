# Android OAuth Setup Checklist

## ✅ What You Need to Do

### 1. Google Cloud Console Configuration

1. **Go to [Google Cloud Console](https://console.cloud.google.com/)**
2. **Navigate to APIs & Services → Credentials**
3. **Find your NEW Android OAuth Client ID**
4. **Add these Authorized redirect URIs:**
   ```
   https://zlivouxymzpbyoxnwxrp.supabase.co/auth/v1/callback
   https://auth.expo.io/@geoattract/app-plantconnects-290725/--/auth/callback
   https://auth.expo.io/@geoattract/app-plantconnects-290725
   http://localhost:8081
   https://localhost:8081
   ```

### 2. Update Supabase Configuration

1. **Go to [Supabase Dashboard](https://supabase.com/dashboard)**
2. **Select your project: `zlivouxymzpbyoxnwxrp`**
3. **Go to Authentication → Providers → Google**
4. **Update with your NEW Android OAuth credentials:**
   - **Client ID**: Your new Android OAuth client ID
   - **Client Secret**: Your new Android OAuth client secret

### 3. Update Your Local Files

Replace these placeholders in your project files:

#### In `.env.local` and `.env.production`:
```
GOOGLE_ANDROID_CLIENT_ID=YOUR_ACTUAL_NEW_ANDROID_CLIENT_ID
```

#### In `google-services.json`:
- Replace `YOUR_NEW_ANDROID_CLIENT_ID_HERE` with your actual client ID
- Replace `YOUR_API_KEY_HERE` with your actual API key
- Replace `SHA1_CERTIFICATE_HASH_HERE` with your actual certificate hash

### 4. Get Your Certificate Hash (for google-services.json)

For development (debug keystore):
```bash
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

For production, use your release keystore.

### 5. Test the Setup

1. **Clear app cache/data in Android simulator**
2. **Restart your development server:**
   ```bash
   npx expo start --clear
   ```
3. **Try Google sign-in**
4. **Check console logs for:**
   - Redirect URI being generated
   - OAuth URL creation
   - Callback parameters received

## 🔍 Debugging

If you still get "Missing authentication tokens in callback":

1. **Check console logs** for the exact redirect URI being generated
2. **Verify the redirect URI** matches exactly what's in Google Cloud Console
3. **Ensure Supabase** has the correct client ID and secret
4. **Test in both** Expo Go and a development build

## 📱 Expected Flow

1. User taps "Continue with Google"
2. App generates redirect URI (logged to console)
3. App opens browser with Google OAuth URL
4. User signs in with Google
5. Google redirects to Supabase callback URL
6. Supabase processes OAuth and redirects to your app
7. App receives tokens and completes authentication
8. User is signed in and redirected to main app

## 🚨 Common Issues

- **Redirect URI mismatch**: Ensure exact match in Google Console
- **Wrong client ID**: Make sure you're using the Android client ID, not web
- **Missing certificate hash**: Required for Android OAuth clients
- **Supabase not updated**: Must use new client ID and secret in Supabase

## ✅ Success Indicators

- Console shows correct redirect URI
- No "Missing authentication tokens" error
- User successfully signs in and reaches main app
- Auth state persists across app restarts

# Google OAuth Setup for PlantConnects Mobile App (iOS/Android)

## ✅ NEW ANDROID OAUTH CLIENT SETUP

You've created a new Android OAuth client ID specifically for mobile apps. This is the correct approach!

## Required Configuration Steps

### 1. Configure Your New Android OAuth Client in Google Cloud Console

For your **NEW Android OAuth Client ID**, add these **Authorized redirect URIs**:

#### Primary Supabase Callback (CRITICAL)
```
https://zlivouxymzpbyoxnwxrp.supabase.co/auth/v1/callback
```

#### Development URLs (for Expo Go testing)
```
https://auth.expo.io/@geoattract/app-plantconnects-290725/--/auth/callback
https://auth.expo.io/@geoattract/app-plantconnects-290725
```

#### Local Development URLs
```
http://localhost:8081
https://localhost:8081
```

### 2. Update Supabase Configuration

You need to update your Supabase project with the new Android OAuth client credentials:

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project: `zlivouxymzpbyoxnwxrp`
3. Go to **Authentication** → **Providers** → **Google**
4. Update:
   - **Client ID**: Your new Android OAuth client ID
   - **Client Secret**: Your new Android OAuth client secret

## ✅ What I've Fixed in the App

1. **Updated Supabase Configuration**
   - Changed `site_url` from `https://plantconnects.com` to `https://auth.expo.io/@geoattract/app-plantconnects-290725`
   - Removed plantconnects.com from allowed redirect URIs
   - Configured for mobile-only OAuth flow

2. **Updated App Configuration**
   - Removed `plantconnects.com` references from `app.json` and `app.config.js`
   - Removed associated domains and intent filters pointing to external website
   - Kept mobile-specific scheme: `plantconnects://`

3. **Added Debugging**
   - Enhanced callback handler with better logging
   - Created test scripts to verify OAuth configuration

## 🔧 What You Need to Do in Google Cloud Console

1. **Go to Google Cloud Console**
   - Navigate to: https://console.cloud.google.com/
   - Select your project

2. **Go to OAuth 2.0 Client IDs**
   - Navigate to: APIs & Services > Credentials
   - Find your OAuth 2.0 Client ID: `13650106015-j2n38d7kmh8berrj6itd2ef8aidnijt2.apps.googleusercontent.com`

3. **Add These Redirect URIs** (CRITICAL STEP)
   - Click on your OAuth client
   - In the "Authorized redirect URIs" section, add:
     - `https://xvgmitjuwgmvllgompsm.supabase.co/auth/v1/callback` ⭐ **MOST IMPORTANT**
     - `https://auth.expo.io/@geoattract/app-plantconnects-290725`
     - `http://localhost:8081`
     - `https://localhost:8081`

4. **Save the configuration**

## Why This Fixes the Issue

- Google OAuth requires exact redirect URI matches
- When Supabase handles OAuth, it redirects to its own callback URL first
- Supabase then processes the tokens and redirects to your app
- Without the correct Supabase callback URL in Google's configuration, the OAuth flow fails

## Testing After Fix

1. Clear your app's cache/data
2. Try the Google sign-in again
3. The flow should now work correctly

## ✅ Current Mobile-Only Supabase Configuration

✅ Site URL: `https://auth.expo.io/@geoattract/app-plantconnects-290725`
✅ Allowed redirect URLs:
- `https://auth.expo.io/@geoattract/app-plantconnects-290725`
- `plantconnects://auth/callback`
- `exp://*.exp.direct`
- `http://localhost:*`
- `exp://192.168.*:*`
- `exp://10.*:*`
- `exp://172.*:*`
- `https://localhost:*`

✅ The Supabase side is now correctly configured for mobile-only usage.
❌ The issue is on the Google OAuth client side - you need to add the redirect URIs above.

## 🧪 Testing

After adding the redirect URIs to Google Cloud Console:

1. **Clear your app's cache/data**
2. **Try the Google sign-in again**
3. **The flow should now work correctly:**
   - App → Google OAuth
   - Google → Supabase callback ✅ (now allowed)
   - Supabase → Your mobile app with tokens ✅

## 📱 Expected Mobile Flow

1. User taps "Sign in with Google" in your mobile app
2. App opens browser/WebView with Google OAuth URL
3. User signs in with Google
4. Google redirects to Supabase callback URL
5. Supabase processes the OAuth and redirects back to your app
6. App receives tokens and completes authentication
7. User is signed in and redirected to main app screen

**No more redirects to plantconnects.com!** 🎉

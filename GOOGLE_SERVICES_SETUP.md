# Google Services Setup for EAS Build

## Problem Fixed
The EAS build was failing with the error: `"google-services.json" is missing, make sure that the file exists.`

## Solution
We've configured the project to use EAS environment variables to provide the `google-services.json` file content during builds, keeping sensitive configuration out of the repository.

## Setup Instructions

### 1. Get your google-services.json file
1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project (or create one if needed)
3. Go to Project Settings (gear icon)
4. In the "Your apps" section, find your Android app
5. Download the `google-services.json` file

### 2. Set up EAS Environment Variable
You need to set the `GOOGLE_SERVICES_JSON` environment variable in EAS with the content of your `google-services.json` file.

#### Option A: Using EAS CLI (Recommended)
```bash
# Set the environment variable with the file content
eas secret:create --scope project --name GOOGLE_SERVICES_JSON --value "$(cat path/to/your/google-services.json)"
```

#### Option B: Using EAS Dashboard
1. Go to [expo.dev](https://expo.dev)
2. Navigate to your project
3. Go to "Secrets" in the left sidebar
4. Click "Create" and add:
   - **Name**: `GOOGLE_SERVICES_JSON`
   - **Value**: Copy and paste the entire content of your `google-services.json` file

### 3. Verify the Setup
The `google-services.json` file should contain something like this:
```json
{
  "project_info": {
    "project_number": "123456789",
    "project_id": "your-project-id",
    "storage_bucket": "your-project-id.appspot.com"
  },
  "client": [
    {
      "client_info": {
        "mobilesdk_app_id": "1:123456789:android:abcdef123456",
        "android_client_info": {
          "package_name": "com.plantconnects.app"
        }
      },
      "oauth_client": [
        {
          "client_id": "13650106015-ug8d1pcrhjpakqb5pirb85s8n7ejpbju.apps.googleusercontent.com",
          "client_type": 1,
          "android_info": {
            "package_name": "com.plantconnects.app",
            "certificate_hash": "your_certificate_hash"
          }
        }
      ],
      "api_key": [
        {
          "current_key": "your_api_key"
        }
      ],
      "services": {
        "appinvite_service": {
          "other_platform_oauth_client": [
            {
              "client_id": "13650106015-j2n38d7kmh8berrj6itd2ef8aidnijt2.apps.googleusercontent.com",
              "client_type": 3
            }
          ]
        }
      }
    }
  ],
  "configuration_version": "1"
}
```

### 4. Build Your App
Now you can build your app with EAS:
```bash
# For preview builds
eas build --platform android --profile preview

# For production builds
eas build --platform android --profile production
```

## How It Works

1. **EAS Build Process**: When EAS builds your app, it runs the `prebuildCommand` specified in `eas.json`
2. **Build Hook**: The `scripts/eas-build-pre-install.sh` script runs and creates `google-services.json` from the `GOOGLE_SERVICES_JSON` environment variable
3. **App Configuration**: The `app.config.js` references the created `google-services.json` file
4. **Build Success**: The Android build process can now find the required Google Services configuration

## Security Benefits

- ✅ Sensitive Google Services configuration is not stored in your repository
- ✅ Different configurations can be used for different build profiles
- ✅ Team members don't need access to the actual `google-services.json` file
- ✅ The configuration is securely managed through EAS

## Troubleshooting

### Build still fails with missing google-services.json
1. Verify the environment variable is set: `eas secret:list`
2. Check that the JSON content is valid
3. Ensure the package name in `google-services.json` matches your app's package name: `com.plantconnects.app`

### Invalid JSON format
Make sure the `GOOGLE_SERVICES_JSON` environment variable contains valid JSON. You can validate it at [jsonlint.com](https://jsonlint.com/).

### Wrong package name
The `package_name` in your `google-services.json` must match the `android.package` in your `app.config.js`:
- App config: `"package": "com.plantconnects.app"`
- Google Services: `"package_name": "com.plantconnects.app"`

## Files Modified

- `eas.json`: Added environment variable and prebuild command
- `app.config.js`: Made Google Services file conditional
- `scripts/eas-build-pre-install.sh`: Created build hook to generate the file
- `.gitignore`: Should already ignore `google-services.json` (recommended)

## Next Steps

After setting up the environment variable, your EAS builds should work without the missing `google-services.json` error.

# OAuth Setup Guide for Expo Go + Production Builds

## Problem Solved
This fix allows Google OAuth to work in both:
- ✅ Expo Go during development (with tunneling)
- ✅ Production iOS/Android apps

## Google OAuth Console Configuration

You need to add both redirect URIs to your Google OAuth Console:

### 1. Development (Expo Go)
Add this redirect URI for development:
```
https://auth.expo.io/@geoattract/app-plantconnects-290725
```

### 2. Production (iOS/Android Apps)
Your production redirects are handled automatically by your custom scheme:
```
plantconnects://auth/callback
```

## How It Works

### Development Mode (`__DEV__ = true`)
- Uses Expo's auth.expo.io proxy automatically
- No custom scheme specified - Expo handles the scheme
- Perfect for Expo Go with tunneling

### Production Mode (`__DEV__ = false`)
- Uses direct custom scheme redirect  
- Uses `scheme: 'plantconnects'` - Your app's custom scheme
- Works in iOS App Store and Android Play Store builds

## Testing

### In Expo Go:
1. Run `npx expo start --tunnel --clear`
2. Open in Expo Go
3. Test Google OAuth - should now redirect properly

### In Production:
1. Build with EAS: `eas build --platform ios` or `eas build --platform android`
2. Install the built app
3. Test Google OAuth - will use custom scheme redirect

## Code Changes Made

1. **Updated redirect URI logic** in `app/auth/login.tsx`:
   - Made conditional redirect URI generation
   - Development: No scheme specified (uses Expo proxy)
   - Production: Uses 'plantconnects' scheme

2. **Updated password reset redirect** for consistency

The app now intelligently switches between development and production OAuth flows!

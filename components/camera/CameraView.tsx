import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Platform, ActivityIndicator } from 'react-native';
import { CameraView as ExpoCameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { Camera, Image as ImageIcon } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import * as ImagePicker from 'expo-image-picker';
import { Colors } from '@/constants/colors';
import { Button } from '@/components/ui/Button';

interface BloomSnapCameraProps {
  onCapture: (uri: string) => void;
  onCancel: () => void;
  mode?: 'identify' | 'diagnose';
}

export const BloomSnapCamera: React.FC<BloomSnapCameraProps> = ({ onCapture, onCancel, mode = 'identify' }) => {
  const [cameraType, setCameraType] = useState<CameraType>('back');
  const [isCapturing, setIsCapturing] = useState(false);
  const [permission, requestPermission] = useCameraPermissions();
  const cameraRef = useRef<ExpoCameraView | null>(null);

  useEffect(() => {
    if (!permission?.granted) {
      requestPermission();
    }
  }, [permission, requestPermission]);

  const handleCapture = async () => {
    if (cameraRef.current && !isCapturing) {
      try {
        setIsCapturing(true);
        // Removed snap sound/haptic feedback

        const photo = await cameraRef.current.takePictureAsync({
          quality: 1,
          base64: false,
          skipProcessing: false,
          // Explicitly disable shutter sound
          mute: true,
        });
        onCapture(photo.uri);
      } catch (error) {
        console.error('Error capturing image:', error);
      } finally {
        setIsCapturing(false);
      }
    }
  };

  const toggleCameraType = () => {
    setCameraType((current: CameraType) => (current === 'back' ? 'front' : 'back'));
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const pickImage = async () => {
    // Handle platform differences for mediaTypes
    const mediaTypes = Platform.OS === 'web'
      ? 'images' as any  // Web uses string format
      : ImagePicker.MediaTypeOptions?.Images || 'images' as any; // Native uses enum or fallback to string

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      onCapture(result.assets[0].uri);
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    }
  };

  if (!permission) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.permissionText}>We need camera permission to identify plants</Text>
        <Button 
          title="Grant Permission" 
          onPress={requestPermission} 
          style={styles.permissionButton}
        />
        <Button 
          title="Cancel" 
          variant="text" 
          onPress={onCancel} 
          style={styles.cancelButton}
        />
      </View>
    );
  }

  return (
    <View style={styles.container} testID="camera-screen">
      <ExpoCameraView
        ref={cameraRef}
        style={styles.camera}
        facing={cameraType}
      >
        <View style={styles.overlay}>
          <View style={styles.header}>
            <TouchableOpacity 
              style={styles.cancelButton} 
              onPress={onCancel}
              testID="camera-cancel-button"
            >
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.targetBox}>
            <View style={styles.targetBoxInner} />
            <Text style={styles.modeIndicator}>
              {mode === 'identify' ? 'Position plant in frame' : 'Focus on problem area'}
            </Text>
          </View>
          
          <View style={styles.footer}>
            <TouchableOpacity 
              style={styles.galleryButton} 
              onPress={pickImage}
              testID="gallery-button"
            >
              <ImageIcon size={24} color={Colors.background} />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.captureButton} 
              onPress={handleCapture}
              disabled={isCapturing}
              testID="capture-button"
            >
              {isCapturing ? (
                <ActivityIndicator size="small" color={Colors.primary} />
              ) : (
                <View style={styles.captureButtonInner} />
              )}
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.flipButton} 
              onPress={toggleCameraType}
              testID="flip-camera-button"
            >
              <Camera size={24} color={Colors.background} />
            </TouchableOpacity>
          </View>
        </View>
      </ExpoCameraView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'space-between',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    padding: 20,
    paddingTop: 50,
  },
  cancelButton: {
    padding: 10,
  },
  cancelText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '600',
  },
  targetBox: {
    alignSelf: 'center',
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: Colors.background,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  targetBoxInner: {
    width: '80%',
    height: '80%',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 10,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingBottom: 40,
  },
  galleryButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.background,
  },
  flipButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  permissionText: {
    color: Colors.text,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    paddingHorizontal: 40,
  },
  permissionButton: {
    marginBottom: 20,
  },
  modeIndicator: {
    position: 'absolute',
    bottom: -40,
    alignSelf: 'center',
    color: Colors.background,
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
});
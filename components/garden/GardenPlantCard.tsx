import React from 'react';
import { StyleSheet, Text, View, Image } from 'react-native';
import { Colors } from '@/constants/colors';
import { GardenPlant } from '@/types/plant';
import { Card } from '@/components/ui/Card';

interface GardenPlantCardProps {
  plant: GardenPlant;
  onPress: () => void;
}

export const GardenPlantCard: React.FC<GardenPlantCardProps> = ({
  plant,
  onPress,
}) => {
  const formatAddedDate = (date: Date | undefined) => {
    // Use addedDate if available, otherwise fall back to createdAt
    const dateToUse = date || plant.createdAt;
    if (!dateToUse) return 'Recently added';

    const now = new Date();
    const diffTime = Math.abs(now.getTime() - dateToUse.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Added today';
    } else if (diffDays === 1) {
      return 'Added yesterday';
    } else if (diffDays < 7) {
      return `Added ${diffDays} days ago`;
    } else if (diffDays < 30) {
      const weeks = Math.floor(diffDays / 7);
      return `Added ${weeks} week${weeks > 1 ? 's' : ''} ago`;
    } else {
      return `Added ${dateToUse.toLocaleDateString()}`;
    }
  };

  const getStatusColor = () => {
    // Status indicator color based on public visibility
    return plant.isPublic ? Colors.success : Colors.textMuted;
  };

  const getHealthStatusColor = () => {
    switch (plant.healthStatus) {
      case 'healthy':
        return Colors.success;
      case 'sick':
        return Colors.error;
      case 'recovering':
        return Colors.accent1;
      case 'critical':
        return Colors.error;
      default:
        return Colors.textMuted;
    }
  };

  const getHealthStatusText = () => {
    switch (plant.healthStatus) {
      case 'healthy':
        return 'Healthy';
      case 'sick':
        return 'Sick';
      case 'recovering':
        return 'Recovering';
      case 'critical':
        return 'Critical';
      default:
        return 'Unknown';
    }
  };

  const truncateNotes = (notes: string | undefined, maxLength: number = 80) => {
    if (!notes) return '';
    if (notes.length <= maxLength) return notes;
    return notes.substring(0, maxLength).trim() + '...';
  };

  return (
    <Card onPress={onPress} style={styles.container} testID={`garden-plant-${plant.id}`}>
      <View style={styles.imageContainer}>
        <Image source={{ uri: plant.imageUrl }} style={styles.image} />
        <View style={[styles.statusIndicator, { backgroundColor: getStatusColor() }]} />
      </View>
      <View style={styles.content}>
        <View>
          <Text style={styles.nickname}>{plant.nickname || plant.commonName}</Text>
          <Text style={styles.scientificName}>{plant.scientificName}</Text>

          {/* Health Status */}
          <View style={styles.healthStatusContainer}>
            <Text style={[styles.healthStatusText, { color: getHealthStatusColor() }]}>
              {getHealthStatusText()}
            </Text>
          </View>

          {/* Location */}
          {(plant.location || plant.locationInGarden) && (
            <Text style={styles.locationText}>
              📍 {plant.locationInGarden || plant.location}
            </Text>
          )}

          {/* Notes */}
          {plant.notes && (
            <Text style={styles.notesText}>
              {truncateNotes(plant.notes)}
            </Text>
          )}
        </View>

        <View style={styles.dateInfo}>
          <Text style={styles.dateText}>
            {formatAddedDate(plant.addedDate)}
          </Text>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
    marginVertical: 6,
    marginHorizontal: 16,
    backgroundColor: Colors.background,
    borderRadius: 12,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 16,
  },
  image: {
    width: 90,
    height: 90,
    borderRadius: 12,
  },
  statusIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 18,
    height: 18,
    borderRadius: 9,
    borderWidth: 3,
    borderColor: Colors.background,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
  },
  nickname: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 4,
  },
  scientificName: {
    fontSize: 14,
    fontStyle: 'italic',
    color: Colors.textLight,
    marginBottom: 2,
  },
  healthStatusContainer: {
    marginTop: 4,
  },
  healthStatusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  locationText: {
    fontSize: 12,
    color: Colors.textMuted,
    marginTop: 4,
    lineHeight: 16,
  },
  notesText: {
    fontSize: 12,
    color: Colors.textMuted,
    marginTop: 4,
    lineHeight: 16,
  },
  dateInfo: {
    marginTop: 8,
  },
  dateText: {
    fontSize: 12,
    color: Colors.textMuted,
  },
});
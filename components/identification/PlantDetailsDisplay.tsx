import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import {
  ChevronDown,
  ChevronUp,
  Leaf,
  AlertTriangle,
  MapPin,
  Calendar,
  Flower,
  TreePine,
  Scissors,
  Users,
  Lightbulb,
} from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { Card } from '@/components/ui/Card';
import { Plant } from '@/types/plant';

interface PlantDetailsDisplayProps {
  plant: Plant;
  confidence: number;
  isDiagnosis?: boolean;
}

export const PlantDetailsDisplay: React.FC<PlantDetailsDisplayProps> = ({
  plant,
  confidence,
  isDiagnosis = false,
}) => {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

  const toggleSection = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey],
    }));
  };

  const renderExpandableSection = (
    title: string,
    content: React.ReactNode,
    sectionKey: string,
    icon?: React.ReactNode
  ) => (
    <Card style={styles.expandableCard}>
      <TouchableOpacity
        style={styles.expandableHeader}
        onPress={() => toggleSection(sectionKey)}
      >
        {icon && <View style={styles.sectionIcon}>{icon}</View>}
        <Text style={styles.expandableTitle}>{title}</Text>
        {expandedSections[sectionKey] ? (
          <ChevronUp size={20} color={Colors.primary} />
        ) : (
          <ChevronDown size={20} color={Colors.primary} />
        )}
      </TouchableOpacity>
      {expandedSections[sectionKey] && (
        <View style={styles.expandableContent}>
          {content}
        </View>
      )}
    </Card>
  );

  const renderToxicityWarning = () => {
    if (!plant.toxicityLevel || plant.toxicityLevel === 'none') return null;

    const getWarningColor = () => {
      switch (plant.toxicityLevel) {
        case 'mild': return Colors.warning;
        case 'moderate': return '#FF8C00';
        case 'severe': return Colors.error;
        default: return Colors.warning;
      }
    };

    const warningCardStyle = {
      ...styles.warningCard,
      borderColor: getWarningColor(),
    };

    return (
      <Card style={warningCardStyle}>
        <View style={styles.warningHeader}>
          <AlertTriangle size={20} color={getWarningColor()} />
          <Text style={[styles.warningTitle, { color: getWarningColor() }]}>
            Toxicity Warning
          </Text>
        </View>
        <Text style={styles.warningText}>
          {plant.toxicityWarning || 'Keep away from pets and children.'}
        </Text>
      </Card>
    );
  };


  return (
    <View style={styles.container}>
      {/* Main Plant Information */}
      <Card style={styles.mainCard}>
        <View style={styles.plantHeader}>
          <Text style={styles.commonName}>{plant.commonName}</Text>
          <Text style={styles.scientificName}>{plant.scientificName}</Text>

          {plant.plantType && (
            <View style={styles.plantTypeContainer}>
              <Leaf size={16} color={Colors.primary} />
              <Text style={styles.plantType}>{plant.plantType}</Text>
            </View>
          )}

          <View style={styles.confidenceContainer}>
            <Text style={styles.confidenceLabel}>Confidence: </Text>
            <Text style={styles.confidenceValue}>{Math.round(confidence * 100)}%</Text>
          </View>
        </View>

        {/* Toxicity Warning */}
        {renderToxicityWarning()}

        {/* Basic Plant Info */}
<Card style={styles.infoCard}>
  <Text style={styles.sectionTitle}>Plant Information</Text>
  <View style={styles.infoRow}>
    <MapPin size={16} color={Colors.primary} />
    <Text style={styles.infoLabel}>Native Region:</Text>
    <Text style={styles.infoValue}>{plant.nativeRegion || 'Unknown'}</Text>
  </View>
  <View style={styles.infoRow}>
    <TreePine size={16} color={Colors.primary} />
    <Text style={styles.infoLabel}>Growth Habit:</Text>
    <Text style={styles.infoValue}>{plant.growthHabit || 'Unknown'}</Text>
  </View>
  <View style={styles.infoRow}>
    <Text style={styles.infoLabel}>Growth Rate:</Text>
    <Text style={styles.infoValue}>{plant.growthRate || 'Unknown'}</Text>
  </View>
  <View style={styles.infoRow}>
    <Text style={styles.infoLabel}>Hardiness Zones:</Text>
    <Text style={styles.infoValue}>{plant.hardinessZones || 'N/A'}</Text>
  </View>
  <View style={styles.infoRow}>
    <Text style={styles.infoLabel}>Minimum Temperature:</Text>
    <Text style={styles.infoValue}>{plant.minTemperature || 'N/A'}</Text>
  </View>
</Card>
      </Card>


      {/* Plant Description */}
<Card style={styles.descriptionCard}>
  <Text style={styles.sectionTitle}>About This Plant</Text>
  <Text style={styles.description}>{plant.description}</Text>
  {plant.matureHeight && plant.matureWidth && (
    <Text style={styles.description}>
      Mature Size: {plant.matureHeight} tall × {plant.matureWidth} wide
    </Text>
  )}
  {plant.bloomTime && plant.bloomTime !== 'Non-flowering' && (
    <Text style={styles.description}>
      Bloom Time: {plant.bloomTime}
      {plant.flowerColors && plant.flowerColors.length > 0 &&
        ` (${plant.flowerColors.join(', ')})`}
    </Text>
  )}
  {plant.foliageType && (
    <Text style={styles.description}>Foliage Type: {plant.foliageType}</Text>
  )}
  {plant.toxicityWarning && (
    <Text style={styles.description}>Toxicity Warning: {plant.toxicityWarning}</Text>
  )}
</Card>

      {/* Tags */}
      {plant.tags && plant.tags.length > 0 && (
        <Card style={styles.tagsCard}>
          <Text style={styles.sectionTitle}>Tags</Text>
          <View style={styles.tagsContainer}>
            {plant.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>
        </Card>
      )}

      {/* Enhanced Information Sections */}
      {/* Enhanced Information Sections */}
      {!isDiagnosis && (
        <>
          {/* Plant Characteristics */}
{renderExpandableSection(
  "Plant Characteristics",
  <View>
    <Text style={styles.characteristicLabel}>Key Traits:</Text>
    <Text style={styles.characteristicValue}>
      {[
        plant.growthHabit && `Growth Habit: ${plant.growthHabit}`,
        plant.matureHeight && plant.matureWidth && `Mature Size: ${plant.matureHeight} × ${plant.matureWidth}`,
        plant.bloomTime && plant.bloomTime !== 'Non-flowering' && `Bloom Time: ${plant.bloomTime}${plant.flowerColors && plant.flowerColors.length > 0 ? ` (${plant.flowerColors.join(', ')})` : ''}`,
        plant.foliageType && `Foliage Type: ${plant.foliageType}`,
        plant.hardinessZones && `Hardiness Zones: ${plant.hardinessZones}${plant.minTemperature ? ` (min ${plant.minTemperature})` : ''}`,
      ].filter(Boolean).join('\n')}
    </Text>
  </View>,
  "characteristics",
  <Leaf size={20} color={Colors.primary} />
)}

          {/* Seasonal Care */}
{renderExpandableSection(
  "Seasonal Care",
  <View>
    <Text style={styles.expandableText}>{plant.seasonalCare || 'No specific seasonal care information available.'}</Text>
    {plant.careInstructions && (
      <>
        <Text style={styles.expandableText}>Water: {plant.careInstructions.water}</Text>
        <Text style={styles.expandableText}>Light: {plant.careInstructions.light}</Text>
        <Text style={styles.expandableText}>Humidity: {plant.careInstructions.humidity}</Text>
        <Text style={styles.expandableText}>
          Temperature: {plant.careInstructions.temperature.min}–{plant.careInstructions.temperature.max}°{plant.careInstructions.temperature.unit}
        </Text>
        <Text style={styles.expandableText}>Soil: {plant.careInstructions.soil}</Text>
        <Text style={styles.expandableText}>Fertilizer: {plant.careInstructions.fertilizer}</Text>
        {plant.maintenanceLevel && (
          <Text style={styles.expandableText}>Maintenance Level: {plant.maintenanceLevel}</Text>
        )}
      </>
    )}
  </View>,
  "seasonal",
  <Calendar size={20} color={Colors.primary} />
)}

          {/* Propagation */}
{renderExpandableSection(
  "Propagation",
  <Text style={styles.expandableText}>{plant.propagation || 'Propagation details not available.'}</Text>,
  "propagation",
  <Scissors size={20} color={Colors.primary} />
)}

          {/* Uses & Benefits */}
{plant.uses && Array.isArray(plant.uses) && plant.uses.length > 0 && renderExpandableSection(
  "Uses & Benefits",
  <View>
    {plant.uses.map((use: string, index: number) => (
      <Text key={index} style={styles.expandableText}>• {use}</Text>
    ))}
  </View>,
  "uses",
  <Lightbulb size={20} color={Colors.primary} />
)}

          {/* Fun Facts */}
{plant.funFacts && Array.isArray(plant.funFacts) && plant.funFacts.length > 0 && renderExpandableSection(
  "Fun Facts",
  <View>
    {plant.funFacts.map((fact: string, index: number) => (
      <Text key={index} style={styles.expandableText}>• {fact}</Text>
    ))}
  </View>,
  "facts",
  <Lightbulb size={20} color={Colors.primary} />
)}

          {/* Companion Plants */}
{plant.companionPlants && Array.isArray(plant.companionPlants) && plant.companionPlants.length > 0 && renderExpandableSection(
  "Companion Plants",
  <View>
    {plant.companionPlants.map((companion: string, index: number) => (
      <Text key={index} style={styles.expandableText}>• {companion}</Text>
    ))}
  </View>,
  "companions",
  <Users size={20} color={Colors.primary} />
)}

          {/* Pests & Diseases */}
{plant.pestsAndDiseases && renderExpandableSection(
  "Pests & Diseases",
  <Text style={styles.expandableText}>{plant.pestsAndDiseases}</Text>,
  "pests",
  <AlertTriangle size={20} color={Colors.warning} />
)}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
  },
  mainCard: {
    marginBottom: 16,
    borderRadius: 16,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  plantHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  commonName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 4,
  },
  scientificName: {
    fontSize: 18,
    fontStyle: 'italic',
    color: Colors.textMuted,
    textAlign: 'center',
    marginBottom: 8,
  },
  plantTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 8,
  },
  plantType: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
    marginLeft: 4,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  confidenceLabel: {
    fontSize: 16,
    color: Colors.textMuted,
  },
  confidenceValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  warningCard: {
    backgroundColor: '#FFF8E1',
    borderWidth: 1,
    borderLeftWidth: 4,
    marginBottom: 16,
  },
  warningHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  warningText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
  infoCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textMuted,
    marginLeft: 8,
    marginRight: 8,
  },
  infoValue: {
    fontSize: 14,
    color: Colors.text,
    flex: 1,
  },
  descriptionCard: {
    marginBottom: 16,
  },
  description: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
  tagsCard: {
    marginBottom: 16,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tagText: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '500',
  },
  expandableCard: {
    marginBottom: 12,
  },
  expandableHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  sectionIcon: {
    marginRight: 8,
  },
  expandableTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    flex: 1,
  },
  expandableContent: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    marginTop: 8,
  },
  expandableText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
    marginBottom: 4,
  },
  characteristicRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  characteristicLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textMuted,
    marginRight: 8,
    minWidth: 100,
  },
  characteristicValue: {
    fontSize: 14,
    color: Colors.text,
    flex: 1,
  },
});

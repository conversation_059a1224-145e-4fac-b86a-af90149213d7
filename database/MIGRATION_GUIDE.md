# Garden Collections Removal Migration Guide

This guide outlines the process for migrating from the old `garden_collections` table structure to the new simplified structure where data is stored directly in `plant_identifications` and `plant_diagnoses` tables.

## Overview

The migration removes the `garden_collections` table and moves all garden-related fields directly into the `plant_identifications` and `plant_diagnoses` tables. This simplifies the data structure and improves performance.

### Key Changes

1. **Removed**: `garden_collections` table
2. **Updated**: `plant_identifications` table with garden fields
3. **Updated**: `plant_diagnoses` table with garden fields
4. **New Logic**: Public/private control via `is_public` field in both tables

## Migration Steps

### Step 1: Schema Updates
Run the following migrations in order:

```sql
-- 1. Add new fields to existing tables
\i database/migrations/005_restructure_for_garden_collections_removal.sql

-- 2. Migrate existing data
\i database/migrations/006_migrate_garden_collections_data.sql

-- 3. Finalize and remove old table (after verification)
\i database/migrations/007_finalize_garden_collections_removal.sql
```

### Step 2: Application Updates

The application code has been updated to work with the new structure:

#### Data Saving Logic
- **Identify Page**: Saves directly to `plant_identifications` with `is_public` field
- **Diagnose Page**: Saves directly to `plant_diagnoses` with `is_public` field
- **Button Logic**:
  - "Add to my Garden & Showcase this Discovery" → `is_public: true`
  - "No! Keep this to myself" → `is_public: false`

#### Data Display Logic
- **Garden Page**: Fetches from both `plant_identifications` and `plant_diagnoses`
- **Identified Tab**: Shows records from `plant_identifications`
- **Diagnosed Tab**: Shows records from `plant_diagnoses`

## New Data Structure

### plant_identifications Table
```sql
-- Existing fields
id, user_id, image_url, scientific_name, common_name, description, 
care_instructions, tags, confidence_score, identification_source, 
is_verified, is_public, location_taken, created_at, updated_at, slug,
plant_type, native_region, toxicity_level, toxicity_warning, 
growth_habit, growth_rate, mature_height, mature_width, 
mature_description, bloom_time, flower_colors, foliage_type, 
hardiness_zones, min_temperature, pests_and_diseases, fun_facts, 
uses, propagation, seasonal_care, companion_plants, maintenance_level

-- New garden fields (moved from garden_collections)
notes, nickname, health_status, location_in_garden, date_acquired,
last_watered, last_fertilized, last_repotted, watering_frequency_days,
fertilizing_frequency_days, allow_community_tips
```

### plant_diagnoses Table
```sql
-- Existing fields
id, user_id, image_url, problem_description, diagnosed_problem,
likely_causes, symptoms_observed, severity, immediate_actions,
long_term_care, product_recommendations, step_by_step_instructions,
prevention_tips, prognosis, confidence_score, diagnosis_source,
is_verified, is_public, notes, created_at, updated_at

-- New garden fields (moved from garden_collections)
location, nickname, health_status, location_in_garden, date_acquired,
last_watered, last_fertilized, last_repotted, watering_frequency_days,
fertilizing_frequency_days, allow_community_tips
```

## Data Migration Details

### Migration Process
1. **Backup**: All data is backed up before migration
2. **Field Mapping**: Garden collection fields are mapped to appropriate tables
3. **Public/Private**: `is_public` field is set based on garden collection settings
4. **Orphaned Data**: Garden collections without plant_identification_id are converted to basic plant identifications

### Data Integrity
- All existing garden data is preserved
- No data loss during migration
- Backup tables are created for rollback if needed

## Verification

After migration, verify the following:

1. **Data Count**: Total records match before and after migration
2. **Field Mapping**: All garden fields are properly populated
3. **Public/Private**: `is_public` field correctly reflects sharing preferences
4. **Application**: All features work with new structure

## Rollback Plan

If rollback is needed:

1. Restore from `garden_collections_backup` table
2. Revert application code changes
3. Remove new fields from plant tables
4. Restore original database structure

## Performance Improvements

The new structure provides:

1. **Fewer Joins**: Direct access to plant data without garden_collections joins
2. **Simpler Queries**: Single table queries for most operations
3. **Better Indexing**: Optimized indexes for common query patterns
4. **Reduced Complexity**: Simplified data model

## Testing Checklist

- [ ] Plant identification saves correctly (public/private)
- [ ] Plant diagnosis saves correctly (public/private)
- [ ] Garden page displays both identified and diagnosed plants
- [ ] Plant updates work for both types
- [ ] Plant removal works for both types
- [ ] Public/private sharing works correctly
- [ ] All existing data is accessible
- [ ] Performance is improved or maintained

## Support

If issues arise during migration:

1. Check migration logs for errors
2. Verify data integrity with provided queries
3. Use backup tables for rollback if necessary
4. Test application functionality thoroughly

The migration is designed to be safe and reversible, with comprehensive backups and verification steps.

-- Add a unique constraint to prevent duplicate plant identifications
-- This will prevent the same user from adding the same plant multiple times
-- We'll use a combination of user_id, scientific_name, and common_name to identify duplicates

ALTER TABLE public.plant_identifications
ADD CONSTRAINT unique_user_plant_identification 
UNIQUE (user_id, scientific_name, common_name);

-- Add a unique constraint to prevent duplicate garden collections
-- This will prevent the same user from adding the same plant to their garden multiple times

ALTER TABLE public.garden_collections
ADD CONSTRAINT unique_user_garden_plant
UNIQUE (user_id, plant_identification_id);

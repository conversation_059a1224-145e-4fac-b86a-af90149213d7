-- Migration to restructure plant_identifications and plant_diagnoses tables
-- to replace garden_collections functionality
-- This migration adds necessary fields to support the new data structure

-- Add missing fields to plant_identifications table
ALTER TABLE public.plant_identifications
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS nickname VARCHAR(255),
ADD COLUMN IF NOT EXISTS health_status VARCHAR(50) DEFAULT 'healthy',
ADD COLUMN IF NOT EXISTS location_in_garden VARCHAR(255),
ADD COLUMN IF NOT EXISTS date_acquired TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS last_watered TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS last_fertilized TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS last_repotted TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS watering_frequency_days INTEGER,
ADD COLUMN IF NOT EXISTS fertilizing_frequency_days INTEGER,
ADD COLUMN IF NOT EXISTS allow_community_tips BOOLEAN DEFAULT false;

-- Add missing fields to plant_diagnoses table  
ALTER TABLE public.plant_diagnoses
ADD COLUMN IF NOT EXISTS location VARCHAR(255),
ADD COLUMN IF NOT EXISTS nickname VARCHAR(255),
ADD COLUMN IF NOT EXISTS health_status VARCHAR(50) DEFAULT 'healthy',
ADD COLUMN IF NOT EXISTS location_in_garden VARCHAR(255),
ADD COLUMN IF NOT EXISTS date_acquired TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS last_watered TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS last_fertilized TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS last_repotted TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS watering_frequency_days INTEGER,
ADD COLUMN IF NOT EXISTS fertilizing_frequency_days INTEGER,
ADD COLUMN IF NOT EXISTS allow_community_tips BOOLEAN DEFAULT false;

-- Add constraints for health_status values
ALTER TABLE public.plant_identifications
ADD CONSTRAINT check_plant_identifications_health_status 
CHECK (health_status IN ('healthy', 'sick', 'recovering', 'critical'));

ALTER TABLE public.plant_diagnoses
ADD CONSTRAINT check_plant_diagnoses_health_status 
CHECK (health_status IN ('healthy', 'sick', 'recovering', 'critical'));

-- Add comments to document the new fields
COMMENT ON COLUMN public.plant_identifications.notes IS 'User personal notes about the plant identification';
COMMENT ON COLUMN public.plant_identifications.nickname IS 'User-given nickname for the plant';
COMMENT ON COLUMN public.plant_identifications.health_status IS 'Current health status of the plant';
COMMENT ON COLUMN public.plant_identifications.location_in_garden IS 'Location of the plant within user garden';
COMMENT ON COLUMN public.plant_identifications.allow_community_tips IS 'Whether to allow community tips for this plant';

COMMENT ON COLUMN public.plant_diagnoses.location IS 'Location where the plant photo was taken';
COMMENT ON COLUMN public.plant_diagnoses.nickname IS 'User-given nickname for the plant';
COMMENT ON COLUMN public.plant_diagnoses.health_status IS 'Current health status of the plant';
COMMENT ON COLUMN public.plant_diagnoses.location_in_garden IS 'Location of the plant within user garden';
COMMENT ON COLUMN public.plant_diagnoses.allow_community_tips IS 'Whether to allow community tips for this plant';

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_plant_identifications_user_public 
ON public.plant_identifications(user_id, is_public);

CREATE INDEX IF NOT EXISTS idx_plant_identifications_user_created 
ON public.plant_identifications(user_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_plant_diagnoses_user_public 
ON public.plant_diagnoses(user_id, is_public);

CREATE INDEX IF NOT EXISTS idx_plant_diagnoses_user_created 
ON public.plant_diagnoses(user_id, created_at DESC);

-- Note: We're not dropping garden_collections table yet to allow for data migration
-- This will be done in a separate migration after data has been migrated

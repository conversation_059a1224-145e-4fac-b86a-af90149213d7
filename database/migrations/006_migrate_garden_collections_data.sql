-- Migration script to move data from garden_collections to plant_identifications and plant_diagnoses
-- This script preserves all existing data while restructuring the database

-- Step 1: Update plant_identifications with garden collection data
-- For identifications that have corresponding garden collections
UPDATE public.plant_identifications 
SET 
  notes = COALESCE(gc.notes, plant_identifications.notes),
  nickname = gc.nickname,
  health_status = gc.health_status,
  location_in_garden = gc.location_in_garden,
  date_acquired = gc.date_acquired::timestamp with time zone,
  last_watered = gc.last_watered::timestamp with time zone,
  last_fertilized = gc.last_fertilized::timestamp with time zone,
  last_repotted = gc.last_repotted::timestamp with time zone,
  watering_frequency_days = gc.watering_frequency_days,
  fertilizing_frequency_days = gc.fertilizing_frequency_days,
  allow_community_tips = gc.allow_community_tips,
  is_public = gc.is_public, -- Update is_public based on garden collection
  updated_at = NOW()
FROM public.garden_collections gc
WHERE plant_identifications.id = gc.plant_identification_id
  AND gc.plant_identification_id IS NOT NULL;

-- Step 2: Update plant_diagnoses with garden collection data
-- For diagnoses that have corresponding garden collections
UPDATE public.plant_diagnoses 
SET 
  notes = COALESCE(plant_diagnoses.notes, gc.notes),
  nickname = gc.nickname,
  health_status = gc.health_status,
  location_in_garden = gc.location_in_garden,
  date_acquired = gc.date_acquired::timestamp with time zone,
  last_watered = gc.last_watered::timestamp with time zone,
  last_fertilized = gc.last_fertilized::timestamp with time zone,
  last_repotted = gc.last_repotted::timestamp with time zone,
  watering_frequency_days = gc.watering_frequency_days,
  fertilizing_frequency_days = gc.fertilizing_frequency_days,
  allow_community_tips = gc.allow_community_tips,
  is_public = gc.is_public, -- Update is_public based on garden collection
  updated_at = NOW()
FROM public.garden_collections gc
WHERE plant_diagnoses.garden_collection_id = gc.id;

-- Step 3: Handle garden collections that don't have plant_identification_id
-- These might be standalone garden entries that need to be preserved
-- We'll create plant_identifications for these if they don't exist

-- First, let's identify garden collections without plant_identification_id
-- and create basic plant identifications for them
INSERT INTO public.plant_identifications (
  user_id,
  image_url,
  scientific_name,
  common_name,
  description,
  identification_source,
  is_verified,
  is_public,
  notes,
  nickname,
  health_status,
  location_in_garden,
  date_acquired,
  last_watered,
  last_fertilized,
  last_repotted,
  watering_frequency_days,
  fertilizing_frequency_days,
  allow_community_tips,
  created_at,
  updated_at
)
SELECT 
  gc.user_id,
  '', -- Empty image_url as we don't have it
  'Unknown', -- Default scientific name
  COALESCE(gc.nickname, 'Unknown Plant'), -- Use nickname as common name
  'Plant added directly to garden', -- Default description
  'manual_entry', -- Source
  false, -- Not verified
  gc.is_public,
  gc.notes,
  gc.nickname,
  gc.health_status,
  gc.location_in_garden,
  gc.date_acquired::timestamp with time zone,
  gc.last_watered::timestamp with time zone,
  gc.last_fertilized::timestamp with time zone,
  gc.last_repotted::timestamp with time zone,
  gc.watering_frequency_days,
  gc.fertilizing_frequency_days,
  gc.allow_community_tips,
  gc.created_at::timestamp with time zone,
  gc.updated_at::timestamp with time zone
FROM public.garden_collections gc
WHERE gc.plant_identification_id IS NULL;

-- Step 4: Create a backup table for garden_collections before dropping
-- This allows for rollback if needed
CREATE TABLE IF NOT EXISTS public.garden_collections_backup AS 
SELECT * FROM public.garden_collections;

-- Add a comment to the backup table
COMMENT ON TABLE public.garden_collections_backup IS 'Backup of garden_collections table before migration to new structure. Can be used for rollback if needed.';

-- Step 5: Log migration statistics
DO $$
DECLARE
  total_garden_collections INTEGER;
  updated_identifications INTEGER;
  updated_diagnoses INTEGER;
  created_identifications INTEGER;
BEGIN
  SELECT COUNT(*) INTO total_garden_collections FROM public.garden_collections;
  
  SELECT COUNT(*) INTO updated_identifications 
  FROM public.plant_identifications pi
  WHERE EXISTS (
    SELECT 1 FROM public.garden_collections gc 
    WHERE gc.plant_identification_id = pi.id
  );
  
  SELECT COUNT(*) INTO updated_diagnoses 
  FROM public.plant_diagnoses pd
  WHERE EXISTS (
    SELECT 1 FROM public.garden_collections gc 
    WHERE gc.id = pd.garden_collection_id
  );
  
  SELECT COUNT(*) INTO created_identifications 
  FROM public.plant_identifications pi
  WHERE pi.identification_source = 'manual_entry';
  
  RAISE NOTICE 'Migration Statistics:';
  RAISE NOTICE 'Total garden collections: %', total_garden_collections;
  RAISE NOTICE 'Updated plant identifications: %', updated_identifications;
  RAISE NOTICE 'Updated plant diagnoses: %', updated_diagnoses;
  RAISE NOTICE 'Created new plant identifications: %', created_identifications;
END $$;

-- Note: The garden_collections table is not dropped in this migration
-- It will be dropped in a separate migration after verification that all data has been migrated correctly

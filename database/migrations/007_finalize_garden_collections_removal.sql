-- Final migration to remove garden_collections table after data migration
-- This script should only be run after verifying that all data has been successfully migrated

-- Step 1: Verify migration was successful
-- Check that all garden collections have been migrated to appropriate tables
DO $$
DECLARE
  total_garden_collections INTEGER;
  migrated_identifications INTEGER;
  migrated_diagnoses INTEGER;
  orphaned_collections INTEGER;
BEGIN
  -- Count total garden collections
  SELECT COUNT(*) INTO total_garden_collections FROM public.garden_collections;
  
  -- Count migrated identifications (those with garden-related fields populated)
  SELECT COUNT(*) INTO migrated_identifications 
  FROM public.plant_identifications pi
  WHERE EXISTS (
    SELECT 1 FROM public.garden_collections gc 
    WHERE gc.plant_identification_id = pi.id
  );
  
  -- Count migrated diagnoses (those with garden-related fields populated)
  SELECT COUNT(*) INTO migrated_diagnoses 
  FROM public.plant_diagnoses pd
  WHERE EXISTS (
    SELECT 1 FROM public.garden_collections gc 
    WHERE gc.id = pd.garden_collection_id
  );
  
  -- Count orphaned collections (those without plant_identification_id)
  SELECT COUNT(*) INTO orphaned_collections 
  FROM public.garden_collections 
  WHERE plant_identification_id IS NULL;
  
  RAISE NOTICE 'Migration Verification:';
  RAISE NOTICE 'Total garden collections: %', total_garden_collections;
  RAISE NOTICE 'Migrated to plant_identifications: %', migrated_identifications;
  RAISE NOTICE 'Migrated to plant_diagnoses: %', migrated_diagnoses;
  RAISE NOTICE 'Orphaned collections (created as new identifications): %', orphaned_collections;
  
  -- Verify that migration is complete
  IF (migrated_identifications + migrated_diagnoses + orphaned_collections) != total_garden_collections THEN
    RAISE EXCEPTION 'Migration verification failed. Not all garden collections have been migrated.';
  END IF;
  
  RAISE NOTICE 'Migration verification successful. All garden collections have been migrated.';
END $$;

-- Step 2: Remove foreign key constraints that reference garden_collections
-- Remove garden_collection_id from plant_diagnoses (it's now deprecated)
ALTER TABLE public.plant_diagnoses DROP COLUMN IF EXISTS garden_collection_id;

-- Step 3: Remove unique constraints that reference garden_collections
ALTER TABLE public.garden_collections DROP CONSTRAINT IF EXISTS unique_user_garden_plant;

-- Step 4: Update any remaining references in other tables
-- Check for any other tables that might reference garden_collections
-- (This is a safety check - there shouldn't be any in this application)

-- Step 5: Drop the garden_collections table
-- First, create a final backup if needed
CREATE TABLE IF NOT EXISTS public.garden_collections_final_backup AS 
SELECT * FROM public.garden_collections;

COMMENT ON TABLE public.garden_collections_final_backup IS 'Final backup of garden_collections table before dropping. Created on ' || NOW()::text;

-- Drop the garden_collections table
DROP TABLE IF EXISTS public.garden_collections CASCADE;

-- Step 6: Clean up any orphaned data or references
-- Remove any views or functions that might reference the dropped table
-- (Add specific cleanup commands here if needed)

-- Step 7: Update database comments and documentation
COMMENT ON TABLE public.plant_identifications IS 'Plant identifications with integrated garden management fields. Replaces the old garden_collections table.';
COMMENT ON TABLE public.plant_diagnoses IS 'Plant diagnoses with integrated garden management fields. Can exist independently or linked to plant identifications.';

-- Step 8: Create indexes for better performance on the new structure
CREATE INDEX IF NOT EXISTS idx_plant_identifications_user_health 
ON public.plant_identifications(user_id, health_status) 
WHERE health_status IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_plant_diagnoses_user_health 
ON public.plant_diagnoses(user_id, health_status) 
WHERE health_status IS NOT NULL;

-- Step 9: Log completion
DO $$
BEGIN
  RAISE NOTICE 'Garden collections table removal completed successfully at %', NOW();
  RAISE NOTICE 'All garden data has been migrated to plant_identifications and plant_diagnoses tables';
  RAISE NOTICE 'The application now uses a simplified data structure without the garden_collections table';
END $$;

-- Step 10: Verify the new structure works
-- Test queries to ensure the new structure supports all required operations

-- Test: Get all plants for a user (both identified and diagnosed)
-- This query should work with the new structure
/*
-- Example query for application to use:
WITH user_plants AS (
  -- Get identified plants
  SELECT 
    id, 'identification' as type, scientific_name, common_name, 
    image_url, nickname, notes, health_status, is_public, created_at
  FROM plant_identifications 
  WHERE user_id = 'USER_ID'
  
  UNION ALL
  
  -- Get diagnosed plants
  SELECT 
    id, 'diagnosis' as type, 'Unknown' as scientific_name, 
    COALESCE(nickname, 'Unknown Plant') as common_name,
    image_url, nickname, notes, health_status, is_public, created_at
  FROM plant_diagnoses 
  WHERE user_id = 'USER_ID'
)
SELECT * FROM user_plants ORDER BY created_at DESC;
*/

RAISE NOTICE 'Migration to new garden structure completed successfully!';

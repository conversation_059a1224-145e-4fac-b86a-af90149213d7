-- Migration: Add plant identification fields to plant_diagnoses table
-- This allows plant_diagnoses to store complete plant data independently
-- without requiring joins to plant_identifications table

-- Add plant identification fields to plant_diagnoses table
ALTER TABLE public.plant_diagnoses
ADD COLUMN IF NOT EXISTS scientific_name <PERSON><PERSON><PERSON><PERSON>(255),
ADD COLUMN IF NOT EXISTS common_name VA<PERSON>HAR(255),
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS plant_type VARCHAR(100),
ADD COLUMN IF NOT EXISTS native_region VARCHAR(255),
ADD COLUMN IF NOT EXISTS toxicity_level VARCHAR(50),
ADD COLUMN IF NOT EXISTS toxicity_warning TEXT,
ADD COLUMN IF NOT EXISTS growth_habit VARCHAR(100),
ADD COLUMN IF NOT EXISTS growth_rate VARCHAR(50),
ADD COLUMN IF NOT EXISTS mature_height VARCHAR(50),
ADD COLUMN IF NOT EXISTS mature_width VARCHAR(50),
ADD COLUMN IF NOT EXISTS mature_description TEXT,
ADD COLUMN IF NOT EXISTS bloom_time VARCHAR(100),
ADD COLUMN IF NOT EXISTS flower_colors TEXT[],
ADD COLUMN IF NOT EXISTS foliage_type VARCHAR(100),
ADD COLUMN IF NOT EXISTS hardiness_zones VARCHAR(50),
ADD COLUMN IF NOT EXISTS min_temperature VARCHAR(50),
ADD COLUMN IF NOT EXISTS pests_and_diseases TEXT,
ADD COLUMN IF NOT EXISTS fun_facts TEXT[],
ADD COLUMN IF NOT EXISTS uses TEXT[],
ADD COLUMN IF NOT EXISTS propagation TEXT,
ADD COLUMN IF NOT EXISTS seasonal_care TEXT,
ADD COLUMN IF NOT EXISTS companion_plants TEXT[],
ADD COLUMN IF NOT EXISTS maintenance_level VARCHAR(50),
ADD COLUMN IF NOT EXISTS tags TEXT[],
ADD COLUMN IF NOT EXISTS confidence_score DECIMAL(3,2) DEFAULT 0.95;

-- Update care_instructions to be JSONB if not already
ALTER TABLE public.plant_diagnoses
ALTER COLUMN care_instructions TYPE JSONB USING care_instructions::jsonb;

-- Add comments for the new fields
COMMENT ON COLUMN public.plant_diagnoses.scientific_name IS 'Scientific name of the diagnosed plant';
COMMENT ON COLUMN public.plant_diagnoses.common_name IS 'Common name of the diagnosed plant';
COMMENT ON COLUMN public.plant_diagnoses.description IS 'Description of the diagnosed plant';
COMMENT ON COLUMN public.plant_diagnoses.plant_type IS 'Type of plant (e.g., houseplant, tree, shrub)';
COMMENT ON COLUMN public.plant_diagnoses.native_region IS 'Native region of the plant';
COMMENT ON COLUMN public.plant_diagnoses.toxicity_level IS 'Toxicity level of the plant';
COMMENT ON COLUMN public.plant_diagnoses.toxicity_warning IS 'Toxicity warning information';
COMMENT ON COLUMN public.plant_diagnoses.growth_habit IS 'Growth habit of the plant';
COMMENT ON COLUMN public.plant_diagnoses.growth_rate IS 'Growth rate of the plant';
COMMENT ON COLUMN public.plant_diagnoses.mature_height IS 'Mature height of the plant';
COMMENT ON COLUMN public.plant_diagnoses.mature_width IS 'Mature width of the plant';
COMMENT ON COLUMN public.plant_diagnoses.mature_description IS 'Description of mature plant characteristics';
COMMENT ON COLUMN public.plant_diagnoses.bloom_time IS 'Blooming time of the plant';
COMMENT ON COLUMN public.plant_diagnoses.flower_colors IS 'Array of flower colors';
COMMENT ON COLUMN public.plant_diagnoses.foliage_type IS 'Type of foliage';
COMMENT ON COLUMN public.plant_diagnoses.hardiness_zones IS 'Plant hardiness zones';
COMMENT ON COLUMN public.plant_diagnoses.min_temperature IS 'Minimum temperature tolerance';
COMMENT ON COLUMN public.plant_diagnoses.pests_and_diseases IS 'Common pests and diseases information';
COMMENT ON COLUMN public.plant_diagnoses.fun_facts IS 'Array of fun facts about the plant';
COMMENT ON COLUMN public.plant_diagnoses.uses IS 'Array of plant uses';
COMMENT ON COLUMN public.plant_diagnoses.propagation IS 'Propagation information';
COMMENT ON COLUMN public.plant_diagnoses.seasonal_care IS 'Seasonal care instructions';
COMMENT ON COLUMN public.plant_diagnoses.companion_plants IS 'Array of companion plants';
COMMENT ON COLUMN public.plant_diagnoses.maintenance_level IS 'Maintenance level required';
COMMENT ON COLUMN public.plant_diagnoses.tags IS 'Array of tags for the plant';
COMMENT ON COLUMN public.plant_diagnoses.confidence_score IS 'Confidence score of the plant identification';

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_plant_diagnoses_scientific_name 
ON public.plant_diagnoses(scientific_name);

CREATE INDEX IF NOT EXISTS idx_plant_diagnoses_common_name 
ON public.plant_diagnoses(common_name);

CREATE INDEX IF NOT EXISTS idx_plant_diagnoses_plant_type 
ON public.plant_diagnoses(plant_type);

-- Update table comment
COMMENT ON TABLE public.plant_diagnoses IS 'Plant diagnoses with complete plant identification data stored independently. No longer requires joins to plant_identifications table.';

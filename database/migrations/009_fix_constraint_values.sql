-- Fix constraint values to match application code
-- This migration fixes the severity and health_status constraints to match the values used in the application

-- Fix severity constraint on plant_diagnoses table
-- Database was expecting: ['low', 'medium', 'high', 'critical']
-- Application sends: ['mild', 'moderate', 'severe', 'critical']
ALTER TABLE public.plant_diagnoses 
DROP CONSTRAINT IF EXISTS plant_diagnoses_severity_check;

ALTER TABLE public.plant_diagnoses 
ADD CONSTRAINT plant_diagnoses_severity_check 
CHECK (severity IS NULL OR severity IN ('mild', 'moderate', 'severe', 'critical'));

-- Fix health_status constraint on plant_diagnoses table
-- Database was expecting: ['healthy', 'sick', 'recovering', 'dead']
-- Application expects: ['healthy', 'sick', 'recovering', 'critical']
ALTER TABLE public.plant_diagnoses 
DROP CONSTRAINT IF EXISTS plant_diagnoses_health_status_check;

ALTER TABLE public.plant_diagnoses 
ADD CONSTRAINT check_plant_diagnoses_health_status 
CHECK (health_status IS NULL OR health_status IN ('healthy', 'sick', 'recovering', 'critical'));

-- Fix health_status constraint on plant_identifications table for consistency
ALTER TABLE public.plant_identifications 
DROP CONSTRAINT IF EXISTS plant_identifications_health_status_check;

ALTER TABLE public.plant_identifications 
ADD CONSTRAINT check_plant_identifications_health_status 
CHECK (health_status IS NULL OR health_status IN ('healthy', 'sick', 'recovering', 'critical'));

-- Add comments to document the constraint values
COMMENT ON CONSTRAINT plant_diagnoses_severity_check ON public.plant_diagnoses 
IS 'Severity levels: mild (minor issues), moderate (noticeable problems), severe (significant damage), critical (plant at risk)';

COMMENT ON CONSTRAINT check_plant_diagnoses_health_status ON public.plant_diagnoses 
IS 'Health status: healthy (good condition), sick (has problems), recovering (improving), critical (severe condition)';

COMMENT ON CONSTRAINT check_plant_identifications_health_status ON public.plant_identifications 
IS 'Health status: healthy (good condition), sick (has problems), recovering (improving), critical (severe condition)';

-- Migration to update database schema and RLS policies for Clerk integration
-- This migration:
-- 1. Changes user_id columns from UUID to TEXT to support <PERSON>'s string-based user IDs
-- 2. Updates all RLS policies to use auth.jwt()->>'sub' instead of auth.uid()
-- 3. Migrates existing data to use string-based user IDs

-- IMPORTANT: This migration will clear existing data as we're changing the user ID format
-- In production, you would need a more sophisticated migration strategy

-- Step 1: Disable RLS temporarily to allow schema changes
ALTER TABLE public.user_profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.plant_identifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.plant_diagnoses DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.community_posts DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_achievements DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.recovery_tracking DISABLE ROW LEVEL SECURITY;

-- Step 2: Drop all existing RLS policies
DROP POLICY IF EXISTS "Anyone can view community posts" ON public.community_posts;
DROP POLICY IF EXISTS "Users can insert their own posts" ON public.community_posts;
DROP POLICY IF EXISTS "Users can update their own posts" ON public.community_posts;
DROP POLICY IF EXISTS "Users can delete their own posts" ON public.community_posts;

DROP POLICY IF EXISTS "Users can view public identifications" ON public.plant_identifications;
DROP POLICY IF EXISTS "Users can insert their own identifications" ON public.plant_identifications;
DROP POLICY IF EXISTS "Users can update their own identifications" ON public.plant_identifications;
DROP POLICY IF EXISTS "Users can delete their own identifications" ON public.plant_identifications;

DROP POLICY IF EXISTS "Users can view public diagnoses" ON public.plant_diagnoses;
DROP POLICY IF EXISTS "Users can insert their own diagnoses" ON public.plant_diagnoses;
DROP POLICY IF EXISTS "Users can update their own diagnoses" ON public.plant_diagnoses;
DROP POLICY IF EXISTS "Users can delete their own diagnoses" ON public.plant_diagnoses;

DROP POLICY IF EXISTS "Users can view public profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can delete their own profile" ON public.user_profiles;

DROP POLICY IF EXISTS "Users can view their own achievements" ON public.user_achievements;
DROP POLICY IF EXISTS "Users can insert their own achievements" ON public.user_achievements;
DROP POLICY IF EXISTS "Users can update their own achievements" ON public.user_achievements;
DROP POLICY IF EXISTS "Users can delete their own achievements" ON public.user_achievements;

DROP POLICY IF EXISTS "Users can view public recovery tracking" ON public.recovery_tracking;
DROP POLICY IF EXISTS "Users can insert their own recovery tracking" ON public.recovery_tracking;
DROP POLICY IF EXISTS "Users can update their own recovery tracking" ON public.recovery_tracking;
DROP POLICY IF EXISTS "Users can delete their own recovery tracking" ON public.recovery_tracking;

-- Garden Collections policies (if table still exists)
DROP POLICY IF EXISTS "Users can view public gardens" ON public.garden_collections;
DROP POLICY IF EXISTS "Users can insert their own garden items" ON public.garden_collections;
DROP POLICY IF EXISTS "Users can update their own garden items" ON public.garden_collections;
DROP POLICY IF EXISTS "Users can delete their own garden items" ON public.garden_collections;

-- Step 3: Clear existing data (since we're changing user ID format)
-- In production, you would migrate this data instead
TRUNCATE TABLE public.recovery_tracking RESTART IDENTITY CASCADE;
TRUNCATE TABLE public.community_posts RESTART IDENTITY CASCADE;
TRUNCATE TABLE public.plant_diagnoses RESTART IDENTITY CASCADE;
TRUNCATE TABLE public.plant_identifications RESTART IDENTITY CASCADE;
TRUNCATE TABLE public.user_achievements RESTART IDENTITY CASCADE;
TRUNCATE TABLE public.user_profiles RESTART IDENTITY CASCADE;

-- Also truncate garden_collections if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'garden_collections') THEN
    EXECUTE 'TRUNCATE TABLE public.garden_collections RESTART IDENTITY CASCADE';
  END IF;
END $$;

-- Step 4: Change user_id columns from UUID to TEXT
-- This is necessary because Clerk uses string-based user IDs

-- Update user_profiles table
ALTER TABLE public.user_profiles ALTER COLUMN user_id TYPE TEXT;

-- Update plant_identifications table
ALTER TABLE public.plant_identifications ALTER COLUMN user_id TYPE TEXT;

-- Update plant_diagnoses table
ALTER TABLE public.plant_diagnoses ALTER COLUMN user_id TYPE TEXT;

-- Update community_posts table
ALTER TABLE public.community_posts ALTER COLUMN user_id TYPE TEXT;

-- Update user_achievements table
ALTER TABLE public.user_achievements ALTER COLUMN user_id TYPE TEXT;

-- Update recovery_tracking table
ALTER TABLE public.recovery_tracking ALTER COLUMN user_id TYPE TEXT;

-- Update garden_collections table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'garden_collections') THEN
    EXECUTE 'ALTER TABLE public.garden_collections ALTER COLUMN user_id TYPE TEXT';
  END IF;
END $$;

-- Step 5: Create new RLS policies with Clerk-compatible conditions

-- Community Posts policies
CREATE POLICY "Anyone can view community posts" ON public.community_posts
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own posts" ON public.community_posts
  FOR INSERT WITH CHECK (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can update their own posts" ON public.community_posts
  FOR UPDATE USING (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can delete their own posts" ON public.community_posts
  FOR DELETE USING (auth.jwt()->>'sub' = user_id);

-- Plant Identifications policies
CREATE POLICY "Users can view public identifications" ON public.plant_identifications
  FOR SELECT USING ((is_public = true) OR (auth.jwt()->>'sub' = user_id));

CREATE POLICY "Users can insert their own identifications" ON public.plant_identifications
  FOR INSERT WITH CHECK (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can update their own identifications" ON public.plant_identifications
  FOR UPDATE USING (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can delete their own identifications" ON public.plant_identifications
  FOR DELETE USING (auth.jwt()->>'sub' = user_id);

-- Plant Diagnoses policies
CREATE POLICY "Users can view public diagnoses" ON public.plant_diagnoses
  FOR SELECT USING ((is_public = true) OR (auth.jwt()->>'sub' = user_id));

CREATE POLICY "Users can insert their own diagnoses" ON public.plant_diagnoses
  FOR INSERT WITH CHECK (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can update their own diagnoses" ON public.plant_diagnoses
  FOR UPDATE USING (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can delete their own diagnoses" ON public.plant_diagnoses
  FOR DELETE USING (auth.jwt()->>'sub' = user_id);

-- User Profiles policies
CREATE POLICY "Users can view public profiles" ON public.user_profiles
  FOR SELECT USING ((is_public = true) OR (auth.jwt()->>'sub' = user_id));

CREATE POLICY "Users can insert their own profile" ON public.user_profiles
  FOR INSERT WITH CHECK (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can update their own profile" ON public.user_profiles
  FOR UPDATE USING (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can delete their own profile" ON public.user_profiles
  FOR DELETE USING (auth.jwt()->>'sub' = user_id);

-- User Achievements policies
CREATE POLICY "Users can view their own achievements" ON public.user_achievements
  FOR SELECT USING (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can insert their own achievements" ON public.user_achievements
  FOR INSERT WITH CHECK (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can update their own achievements" ON public.user_achievements
  FOR UPDATE USING (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can delete their own achievements" ON public.user_achievements
  FOR DELETE USING (auth.jwt()->>'sub' = user_id);

-- Recovery Tracking policies
CREATE POLICY "Users can view public recovery tracking" ON public.recovery_tracking
  FOR SELECT USING ((is_public = true) OR (auth.jwt()->>'sub' = user_id));

CREATE POLICY "Users can insert their own recovery tracking" ON public.recovery_tracking
  FOR INSERT WITH CHECK (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can update their own recovery tracking" ON public.recovery_tracking
  FOR UPDATE USING (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can delete their own recovery tracking" ON public.recovery_tracking
  FOR DELETE USING (auth.jwt()->>'sub' = user_id);

-- Garden Collections policies (if table still exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'garden_collections') THEN
    EXECUTE 'CREATE POLICY "Users can view public gardens" ON public.garden_collections
      FOR SELECT USING ((is_public = true) OR (auth.jwt()->>''sub'' = user_id))';

    EXECUTE 'CREATE POLICY "Users can insert their own garden items" ON public.garden_collections
      FOR INSERT WITH CHECK (auth.jwt()->>''sub'' = user_id)';

    EXECUTE 'CREATE POLICY "Users can update their own garden items" ON public.garden_collections
      FOR UPDATE USING (auth.jwt()->>''sub'' = user_id)';

    EXECUTE 'CREATE POLICY "Users can delete their own garden items" ON public.garden_collections
      FOR DELETE USING (auth.jwt()->>''sub'' = user_id)';
  END IF;
END $$;

-- Step 6: Re-enable RLS on all tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.plant_identifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.plant_diagnoses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.community_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recovery_tracking ENABLE ROW LEVEL SECURITY;

-- Enable RLS on garden_collections if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'garden_collections') THEN
    EXECUTE 'ALTER TABLE public.garden_collections ENABLE ROW LEVEL SECURITY';
  END IF;
END $$;

-- Add comments to document the change
COMMENT ON TABLE public.user_profiles IS 'User profiles table with Clerk-compatible RLS policies using auth.jwt()->>"sub" and TEXT user_id';
COMMENT ON TABLE public.plant_identifications IS 'Plant identifications with Clerk-compatible RLS policies using auth.jwt()->>"sub" and TEXT user_id';
COMMENT ON TABLE public.plant_diagnoses IS 'Plant diagnoses with Clerk-compatible RLS policies using auth.jwt()->>"sub" and TEXT user_id';

RAISE NOTICE 'Database schema and RLS policies updated for Clerk integration:';
RAISE NOTICE '- Changed user_id columns from UUID to TEXT';
RAISE NOTICE '- Updated all RLS policies to use auth.jwt()->>''sub''';
RAISE NOTICE '- Cleared existing data (user IDs are now incompatible)';
RAISE NOTICE '- Re-enabled RLS on all tables';

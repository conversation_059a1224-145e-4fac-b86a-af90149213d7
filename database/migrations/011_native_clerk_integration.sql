-- Migration to implement native Clerk integration
-- This migration updates the user_profiles table to use auth.jwt()->>'sub' as default
-- for automatic user profile creation without webhooks

-- Step 1: Update user_profiles table to use auth.jwt()->>'sub' as default
ALTER TABLE public.user_profiles 
ALTER COLUMN user_id SET DEFAULT auth.jwt()->>'sub';

-- Step 2: Add default values for other required fields to enable automatic profile creation
ALTER TABLE public.user_profiles 
ALTER COLUMN display_name SET DEFAULT 'Plant Lover';

ALTER TABLE public.user_profiles 
ALTER COLUMN is_public SET DEFAULT false;

ALTER TABLE public.user_profiles 
ALTER COLUMN allow_garden_sharing SET DEFAULT false;

ALTER TABLE public.user_profiles 
ALTER COLUMN allow_profile_indexing SET DEFAULT false;

ALTER TABLE public.user_profiles 
ALTER COLUMN experience_level SET DEFAULT 'beginner';

ALTER TABLE public.user_profiles 
ALTER COLUMN total_identifications SET DEFAULT 0;

ALTER TABLE public.user_profiles 
ALTER COLUMN total_diagnoses SET DEFAULT 0;

ALTER TABLE public.user_profiles 
ALTER COLUMN community_points SET DEFAULT 0;

ALTER TABLE public.user_profiles 
ALTER COLUMN achievements SET DEFAULT '[]'::jsonb;

-- Step 3: Update the RLS policy for INSERT to allow automatic profile creation
-- The existing policy should work, but let's make sure it's optimal
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.user_profiles;

CREATE POLICY "Users can insert their own profile" ON public.user_profiles
  FOR INSERT WITH CHECK (
    auth.jwt()->>'sub' = user_id OR 
    user_id IS NULL OR 
    user_id = auth.jwt()->>'sub'
  );

-- Step 4: Add a comment to document the native integration approach
COMMENT ON TABLE public.user_profiles IS 'User profiles table with native Clerk integration using auth.jwt()->>"sub" as default user_id. Profiles are created automatically when users first interact with the database.';

-- Step 5: Create a function to ensure user profile exists (alternative to upsert)
CREATE OR REPLACE FUNCTION public.ensure_user_profile_exists()
RETURNS trigger AS $$
BEGIN
  -- This function can be used as a trigger to automatically create user profiles
  -- when users first interact with other tables
  INSERT INTO public.user_profiles (user_id)
  VALUES (auth.jwt()->>'sub')
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comment to the function
COMMENT ON FUNCTION public.ensure_user_profile_exists() IS 'Trigger function to automatically create user profiles when users first interact with the database';

RAISE NOTICE 'Native Clerk integration migration completed:';
RAISE NOTICE '- Updated user_profiles table with auth.jwt()->>"sub" default';
RAISE NOTICE '- Added default values for required fields';
RAISE NOTICE '- Updated RLS policy for automatic profile creation';
RAISE NOTICE '- Created helper function for profile auto-creation';

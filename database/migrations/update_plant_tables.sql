-- Update plant_identifications table
ALTER TABLE public.plant_identifications
ALTER COLUMN care_instructions TYPE JSONB USING care_instructions::jsonb;

-- Add new columns for additional information
ALTER TABLE public.plant_identifications
ADD COLUMN IF NOT EXISTS plant_type VARCHAR(255),
ADD COLUMN IF NOT EXISTS native_region VARCHAR(255),
ADD COLUMN IF NOT EXISTS toxicity_level VARCHAR(50),
ADD COLUMN IF NOT EXISTS toxicity_warning TEXT,
ADD COLUMN IF NOT EXISTS growth_habit VARCHAR(255),
ADD COLUMN IF NOT EXISTS growth_rate VARCHAR(50),
ADD COLUMN IF NOT EXISTS mature_height VARCHAR(255),
ADD COLUMN IF NOT EXISTS mature_width VARCHAR(255),
ADD COLUMN IF NOT EXISTS mature_description TEXT,
ADD COLUMN IF NOT EXISTS bloom_time VARCHAR(255),
ADD COLUMN IF NOT EXISTS flower_colors TEXT[],
ADD COLUMN IF NOT EXISTS foliage_type VARCHAR(255),
ADD COLUMN IF NOT EXISTS hardiness_zones VARCHAR(255),
ADD COLUMN IF NOT EXISTS min_temperature VARCHAR(255),
ADD COLUMN IF NOT EXISTS pests_and_diseases TEXT,
ADD COLUMN IF NOT EXISTS fun_facts TEXT[],
ADD COLUMN IF NOT EXISTS uses TEXT[],
ADD COLUMN IF NOT EXISTS propagation TEXT,
ADD COLUMN IF NOT EXISTS seasonal_care TEXT,
ADD COLUMN IF NOT EXISTS companion_plants TEXT[],
ADD COLUMN IF NOT EXISTS maintenance_level VARCHAR(50);

-- Update plant_diagnoses table
ALTER TABLE public.plant_diagnoses
ADD COLUMN IF NOT EXISTS diagnosed_problem VARCHAR(255),
ADD COLUMN IF NOT EXISTS likely_causes TEXT[],
ADD COLUMN IF NOT EXISTS symptoms_observed TEXT,
ADD COLUMN IF NOT EXISTS severity VARCHAR(50),
ADD COLUMN IF NOT EXISTS immediate_actions TEXT[],
ADD COLUMN IF NOT EXISTS long_term_care TEXT[],
ADD COLUMN IF NOT EXISTS product_recommendations TEXT[],
ADD COLUMN IF NOT EXISTS step_by_step_instructions TEXT[],
ADD COLUMN IF NOT EXISTS prevention_tips TEXT[],
ADD COLUMN IF NOT EXISTS prognosis TEXT;

-- Update care_instructions in plant_diagnoses table
ALTER TABLE public.plant_diagnoses
ALTER COLUMN care_instructions TYPE JSONB USING care_instructions::jsonb;

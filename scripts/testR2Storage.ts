/**
 * Test script for Cloudflare R2 storage functionality
 * Run this to verify R2 configuration and connectivity
 * COMMENTED OUT FOR PRODUCTION - This is a development test script
 */

// import { StorageService } from '../services/r2Storage';

async function testR2Storage() {
  // console.log('🧪 Testing Cloudflare R2 Storage Configuration...\n');

  try {
    // Test 1: Check storage health
    // console.log('1️⃣ Checking storage health...');
    // const healthCheck = await StorageService.checkStorageHealth();
    // console.log('Health check result:', healthCheck);

    // if (!healthCheck.r2Available) {
    //   console.warn('⚠️  R2 is not available. Check your environment variables:');
    //   console.warn('   - CLOUDFLARE_ACCOUNT_ID');
    //   console.warn('   - R2_ACCESS_KEY_ID');
    //   console.warn('   - R2_SECRET_ACCESS_KEY');
    //   console.warn('   - R2_BUCKET_NAME');
    //   console.warn('   - R2_ENDPOINT_URL');
    //   return;
    // }

    console.log('✅ R2 configuration is valid!\n');

    // Test 2: Create a test image blob
    console.log('2️⃣ Creating test image...');
    const testImageData = createTestImageBlob();
    console.log('Test image created:', { size: testImageData.size, type: testImageData.type });

    // Test 3: Upload test image
    console.log('\n3️⃣ Testing image upload...');
    const testUserId = 'test-user-123';
    const testPath = StorageService.generateImagePath(testUserId, 'scan', 'jpg');
    
    const uploadResult = await StorageService.uploadImage(
      testImageData,
      testPath,
      'image/jpeg'
    );

    if (uploadResult.success) {
      console.log('✅ Upload successful!');
      console.log('   URL:', uploadResult.url);

      // Test 4: Delete test image
      console.log('\n4️⃣ Testing image deletion...');
      const deleteResult = await StorageService.deleteImage(testPath);
      
      if (deleteResult.success) {
        console.log('✅ Delete successful!');
      } else {
        console.error('❌ Delete failed:', deleteResult.error);
      }
    } else {
      console.error('❌ Upload failed:', uploadResult.error);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }

  console.log('\n🏁 R2 storage test completed!');
}

/**
 * Create a simple test image blob (1x1 pixel JPEG)
 */
function createTestImageBlob(): Blob {
  // Minimal JPEG data for a 1x1 pixel red image
  const jpegData = new Uint8Array([
    0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
    0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
    0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
    0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
    0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
    0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
    0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
    0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
    0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
    0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
    0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
    0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x80, 0xFF, 0xD9
  ]);

  return new Blob([jpegData], { type: 'image/jpeg' });
}

// Export for use in other scripts
export { testR2Storage };

// Run test if this file is executed directly
if (require.main === module) {
  testR2Storage().catch(console.error);
}

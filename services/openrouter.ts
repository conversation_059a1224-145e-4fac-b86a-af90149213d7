import { API_CONFIG } from '@/config/api';
import { Plant, CareInstructions } from '@/types/plant';
import * as FileSystem from 'expo-file-system';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';

export interface OpenRouterResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

export interface PlantIdentificationData {
  scientificName: string;
  commonName: string;
  description: string;
  careInstructions: CareInstructions;
  tags: string[];
  confidence: number;
  plantType: string;
  nativeRegion: string;
  toxicity: {
    level: string;
    warning: string;
  };
  growthHabit: string;
  growthRate: string;
  matureSize: {
    height: string;
    width: string;
    description: string;
  };
  bloomTime: string;
  flowerColor: string[];
  foliageType: string;
  hardiness: {
    zones: string;
    minTemperature: string;
  };
  additionalInfo: {
    pestsAndDiseases: string;
    funFacts: string;
    uses: string[];
    propagation: string;
    seasonalCare: string;
    companionPlants: string[];
    maintenanceLevel: string;
  };
  diagnosis?: string;
  treatment?: string;
}

export interface DiagnosisData extends PlantIdentificationData {
  diagnosedProblem: string;
  likelyCauses: string[];
  symptomsObserved: string;
  severity: string;
  immediateActions: string[];
  longTermCare: string[];
  productRecommendations: string[];
  stepByStepInstructions: string[];
  preventionTips: string[];
  prognosis: string;
}

export class OpenRouterService {
  private static async convertImageToBase64(imageUri: string): Promise<string> {
    try {
      // Check if it's already a data URL
      if (imageUri.startsWith('data:')) {
        return imageUri;
      }

      // Check if it's a web URL
      if (imageUri.startsWith('http://') || imageUri.startsWith('https://')) {
        return imageUri;
      }

      // Compress the image before converting to base64
      const compressedImage = await manipulateAsync(
        imageUri,
        [
          // Resize to maximum 1024x1024 while maintaining aspect ratio
          { resize: { width: 1024, height: 1024 } }
        ],
        {
          compress: 0.8, // 80% quality
          format: SaveFormat.JPEG, // Convert to JPEG for better compression
        }
      );

      // Convert compressed image to base64
      const base64 = await FileSystem.readAsStringAsync(compressedImage.uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      return `data:image/jpeg;base64,${base64}`;
    } catch (error) {
      throw new Error('Failed to process image');
    }
  }

  private static async makeRequest(imageUri: string, prompt: string): Promise<string> {
    try {
      // Convert image to base64 data URL if it's a local file
      const processedImageUri = await this.convertImageToBase64(imageUri);

      const response = await fetch(`${API_CONFIG.OPENROUTER_BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_CONFIG.OPENROUTER_API_KEY}`,
          'HTTP-Referer': 'https://plantidguide.com',
          'X-Title': 'PlantIDGuide: Plant Identifier',
        },
        body: JSON.stringify({
          model: API_CONFIG.MODEL,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: prompt,
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: processedImageUri,
                  },
                },
              ],
            },
          ],
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
      }

      const data: OpenRouterResponse = await response.json();
      return data.choices[0]?.message?.content || '';
    } catch (error) {
      throw error;
    }
  }

  static async identifyPlant(imageUri: string): Promise<PlantIdentificationData> {
    const prompt = `Analyze this image carefully. If this is NOT a plant (e.g., it's a person, animal, object, food, etc.), respond with this exact JSON:

{
  "scientificName": "Not a plant",
  "commonName": "Not a plant",
  "description": "This image does not contain a plant. Please take a photo of a plant for identification.",
  "plantType": "Not applicable",
  "nativeRegion": "Not applicable",
  "toxicity": {
    "level": "none",
    "warning": "Not applicable - this is not a plant"
  },
  "growthHabit": "Not applicable",
  "growthRate": "Not applicable",
  "matureSize": {
    "height": "Not applicable",
    "width": "Not applicable",
    "description": "Not applicable - this is not a plant"
  },
  "bloomTime": "Not applicable",
  "flowerColor": ["Not applicable"],
  "foliageType": "Not applicable",
  "hardiness": {
    "zones": "Not applicable",
    "minTemperature": "Not applicable"
  },
  "careInstructions": {
    "light": "medium",
    "water": "medium",
    "temperature": {"min": 18, "max": 25, "unit": "C"},
    "humidity": "medium",
    "soil": "Not applicable",
    "fertilizer": "Not applicable",
    "toxicity": "none"
  },
  "additionalInfo": {
    "pestsAndDiseases": "Not applicable",
    "funFacts": "This appears to be a non-plant image",
    "uses": ["Not applicable"],
    "propagation": "Not applicable",
    "seasonalCare": "Not applicable",
    "companionPlants": ["Not applicable"],
    "maintenanceLevel": "Not applicable"
  },
  "tags": ["not-a-plant"],
  "confidence": 0.95
}

If this IS a plant, provide comprehensive information in the following JSON format:

{
  "scientificName": "Scientific name (Genus species)",
  "commonName": "Most recognizable common name",
  "description": "Detailed description of appearance, size, and key characteristics (3-4 sentences)",
  "plantType": "e.g., Houseplant, Outdoor Plant, Tree, Shrub, Flower, Herb, Succulent, Fern, Vine",
  "nativeRegion": "Where the plant originally comes from",
  "toxicity": {
    "level": "none|mild|moderate|severe",
    "warning": "Clear warning text about toxicity to humans/pets"
  },
  "growthHabit": "e.g., Upright, Bushy, Trailing, Climber, Groundcover",
  "growthRate": "Fast|Moderate|Slow",
  "matureSize": {
    "height": "e.g., 2-3 feet, 6-12 inches, 10-20 feet",
    "width": "e.g., 1-2 feet, 3-6 feet, 8-15 feet",
    "description": "Brief description of mature plant size and spread"
  },
  "bloomTime": "e.g., Spring, Summer, Fall, Winter, Year-round, Non-flowering",
  "flowerColor": ["e.g., White, Pink, Red, Yellow, Purple, Blue, Orange"],
  "foliageType": "e.g., Evergreen, Deciduous, Semi-evergreen",
  "hardiness": {
    "zones": "e.g., 9-11, 4-8, 10-12",
    "minTemperature": "e.g., 50°F (10°C), 32°F (0°C)"
  },
  "careInstructions": {
    "light": "low|medium|high",
    "water": "low|medium|high",
    "temperature": {"min": 15, "max": 30, "unit": "C"},
    "humidity": "low|medium|high",
    "soil": "Detailed soil requirements",
    "fertilizer": "Detailed fertilization needs",
    "toxicity": "none|mild|moderate|severe"
  },
  "additionalInfo": {
    "pestsAndDiseases": "Common pests and diseases this plant faces",
    "funFacts": "Interesting facts or cultural meanings",
    "uses": ["Ornamental", "Edible", "Medicinal", "Air Purifier"],
    "propagation": "How to propagate this plant",
    "seasonalCare": "Seasonal care tips and considerations",
    "companionPlants": ["Plants that grow well together with this plant"],
    "maintenanceLevel": "Low|Medium|High - overall care difficulty"
  },
  "tags": ["relevant", "descriptive", "tags"],
  "confidence": 0.95
}

CRITICAL: Return ONLY the JSON object above, no additional text, explanations, or formatting. Start your response with { and end with }. Do not include any markdown formatting or code blocks.`;

    const response = await this.makeRequest(imageUri, prompt);

    try {
      // Clean the response by removing any markdown formatting
      let cleanedResponse = response.trim();

      // Remove markdown code blocks if present
      cleanedResponse = cleanedResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');

      // Try to extract JSON from the response if it's wrapped in text
      const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : cleanedResponse;

      let parsed;
      try {
        parsed = JSON.parse(jsonString);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        console.error('Raw response:', response);
        console.error('Cleaned response:', cleanedResponse);
        console.error('JSON string:', jsonString);
        throw new Error(`Failed to parse JSON response: ${parseError.message}`);
      }

      // Validate required fields
      if (!parsed.scientificName || !parsed.commonName || !parsed.careInstructions) {
        throw new Error('Missing required fields in response');
      }

      return parsed;
    } catch (error) {
      console.error('Failed to parse OpenRouter response:', error);
      console.error('Raw response was:', response);

      // Check if the response indicates it's not a plant
      const responseText = response.toLowerCase();
      const isNotPlant = responseText.includes('not a plant') ||
                        responseText.includes('no plant') ||
                        responseText.includes('not plant') ||
                        responseText.includes('person') ||
                        responseText.includes('animal') ||
                        responseText.includes('object') ||
                        responseText.includes('food');

      if (isNotPlant) {
        // Return a specific "not a plant" response
        return {
          scientificName: 'Not a plant',
          commonName: 'Not a plant',
          description: 'This image does not appear to contain a plant. Please take a photo of a plant for identification.',
          plantType: 'Not applicable',
          nativeRegion: 'Not applicable',
          toxicity: {
            level: 'none',
            warning: 'Not applicable - this is not a plant'
          },
          growthHabit: 'Not applicable',
          growthRate: 'Not applicable',
          matureSize: {
            height: 'Not applicable',
            width: 'Not applicable',
            description: 'Not applicable - this is not a plant'
          },
          bloomTime: 'Not applicable',
          flowerColor: ['Not applicable'],
          foliageType: 'Not applicable',
          hardiness: {
            zones: 'Not applicable',
            minTemperature: 'Not applicable'
          },
          careInstructions: {
            light: 'medium',
            water: 'medium',
            temperature: { min: 18, max: 25, unit: 'C' },
            humidity: 'medium',
            soil: 'Not applicable',
            fertilizer: 'Not applicable',
            toxicity: 'none'
          },
          additionalInfo: {
            pestsAndDiseases: 'Not applicable',
            funFacts: 'This appears to be a non-plant image',
            uses: ['Not applicable'],
            propagation: 'Not applicable',
            seasonalCare: 'Not applicable',
            companionPlants: ['Not applicable'],
            maintenanceLevel: 'Not applicable'
          },
          tags: ['not-a-plant'],
          confidence: 0.95
        };
      }

      // Return a generic fallback response for parsing errors
      return {
        scientificName: 'Unknown species',
        commonName: 'Unknown plant',
        description: 'Unable to identify this plant. Please try again with a clearer image.',
        plantType: 'Unknown',
        nativeRegion: 'Unknown',
        toxicity: {
          level: 'none',
          warning: 'Toxicity information unavailable'
        },
        growthHabit: 'Unknown',
        growthRate: 'Unknown',
        matureSize: {
          height: 'Unknown',
          width: 'Unknown',
          description: 'Size information unavailable'
        },
        bloomTime: 'Unknown',
        flowerColor: ['Unknown'],
        foliageType: 'Unknown',
        hardiness: {
          zones: 'Unknown',
          minTemperature: 'Unknown'
        },
        careInstructions: {
          light: 'medium',
          water: 'medium',
          temperature: { min: 18, max: 25, unit: 'C' },
          humidity: 'medium',
          soil: 'Well-draining potting mix',
          fertilizer: 'Monthly during growing season',
          toxicity: 'none'
        },
        additionalInfo: {
          pestsAndDiseases: 'Information unavailable',
          funFacts: 'Unable to provide information for unidentified plant',
          uses: ['Unknown'],
          propagation: 'Information unavailable',
          seasonalCare: 'Information unavailable',
          companionPlants: ['Unknown'],
          maintenanceLevel: 'Unknown'
        },
        tags: ['unknown'],
        confidence: 0.1
      };
    }
  }

  static async diagnosePlant(imageUri: string, problemDescription?: string): Promise<DiagnosisData> {
    const problemText = problemDescription ? `The user reports: "${problemDescription}". ` : '';

    const prompt = `You are a professional plant health specialist. Analyze this image carefully. If this is NOT a plant (e.g., it's a person, animal, object, food, etc.), respond with this exact JSON:

{
  "scientificName": "Not a plant",
  "commonName": "Not a plant",
  "description": "This image does not contain a plant. Please take a photo of a plant for diagnosis.",
  "plantType": "Not applicable",
  "nativeRegion": "Not applicable",
  "toxicity": {
    "level": "none",
    "warning": "Not applicable - this is not a plant"
  },
  "growthHabit": "Not applicable",
  "growthRate": "Not applicable",
  "careInstructions": {
    "light": "medium",
    "water": "medium",
    "temperature": {"min": 18, "max": 25, "unit": "C"},
    "humidity": "medium",
    "soil": "Not applicable",
    "fertilizer": "Not applicable",
    "toxicity": "none"
  },
  "additionalInfo": {
    "pestsAndDiseases": "Not applicable",
    "funFacts": "This appears to be a non-plant image",
    "uses": ["Not applicable"],
    "propagation": "Not applicable",
    "seasonalCare": "Not applicable",
    "companionPlants": ["Not applicable"],
    "maintenanceLevel": "Not applicable"
  },
  "tags": ["not-a-plant"],
  "confidence": 0.95,
  "diagnosedProblem": "Not a plant - cannot diagnose",
  "likelyCauses": ["Image does not contain a plant"],
  "symptomsObserved": "No plant symptoms - not a plant image",
  "severity": "Not applicable",
  "immediateActions": ["Take a photo of a plant instead"],
  "longTermCare": ["Not applicable"],
  "productRecommendations": ["Not applicable"],
  "stepByStepInstructions": ["1. Take a photo of a plant", "2. Try diagnosis again"],
  "preventionTips": ["Ensure you are photographing a plant"],
  "prognosis": "Not applicable - this is not a plant"
}

If this IS a plant, analyze it to diagnose health problems, diseases, pests, or care issues. ${problemText}Focus primarily on identifying what's wrong with the plant and providing comprehensive treatment guidance.

Provide your diagnosis in the following JSON format:

{
  "scientificName": "Scientific name (Genus species)",
  "commonName": "Most recognizable common name",
  "description": "Brief description of the plant (2-3 sentences)",
  "plantType": "e.g., Houseplant, Outdoor Plant, Tree, Shrub, Flower, Herb, Succulent, Fern, Vine",
  "nativeRegion": "Where the plant originally comes from",
  "toxicity": {
    "level": "none|mild|moderate|severe",
    "warning": "Clear warning text about toxicity to humans/pets"
  },
  "growthHabit": "e.g., Upright, Bushy, Trailing, Climber, Groundcover",
  "growthRate": "Fast|Moderate|Slow",
  "careInstructions": {
    "light": "low|medium|high",
    "water": "low|medium|high",
    "temperature": {"min": 15, "max": 30, "unit": "C"},
    "humidity": "low|medium|high",
    "soil": "Detailed soil requirements",
    "fertilizer": "Detailed fertilization needs",
    "toxicity": "none|mild|moderate|severe"
  },
  "additionalInfo": {
    "pestsAndDiseases": "Common pests and diseases this plant faces",
    "funFacts": "Interesting facts or cultural meanings",
    "uses": ["Ornamental", "Edible", "Medicinal", "Air Purifier"],
    "propagation": "How to propagate this plant",
    "seasonalCare": "Seasonal care tips and considerations",
    "companionPlants": ["Plants that grow well together with this plant"],
    "maintenanceLevel": "Low|Medium|High - overall care difficulty"
  },
  "tags": ["relevant", "descriptive", "tags"],
  "confidence": 0.95,
  "diagnosedProblem": "Specific health issue (e.g., 'Overwatering', 'Fungal Infection: Powdery Mildew', 'Pest Infestation: Spider Mites', 'Nutrient Deficiency: Nitrogen')",
  "likelyCauses": ["Detailed list of common reasons for this issue", "e.g., Poor drainage", "Too little light", "Contaminated soil", "Lack of air circulation"],
  "symptomsObserved": "Detailed description of what the plant is exhibiting that led to this diagnosis (e.g., 'Yellowing lower leaves', 'White powdery spots on leaves', 'Tiny webs on undersides of leaves', 'Stunted growth')",
  "severity": "Mild|Moderate|Severe",
  "immediateActions": ["What the user should do right away", "e.g., Stop watering immediately", "Isolate the plant", "Prune affected leaves"],
  "longTermCare": ["Changes to watering, light, humidity, soil, etc., to prevent recurrence", "e.g., Adjust watering schedule", "Improve drainage", "Increase humidity"],
  "productRecommendations": ["Specific products if applicable", "e.g., Apply neem oil solution", "Use a fungicide recommended for powdery mildew", "Repot in fresh, well-draining soil"],
  "stepByStepInstructions": ["Clear, numbered list of how to apply the treatment", "1. Remove all affected leaves", "2. Apply neem oil solution to remaining foliage", "3. Adjust watering schedule", "4. Monitor daily for 2 weeks"],
  "preventionTips": ["How to avoid the problem in the future for this specific plant", "e.g., Water only when top inch of soil is dry", "Ensure good air circulation", "Check regularly for early signs"],
  "prognosis": "Brief outlook on the plant's recovery chances (e.g., 'Excellent recovery expected with proper treatment', 'Good prognosis if caught early', 'Recovery possible but may take several weeks')"
}

CRITICAL: The confidence score should reflect your certainty in the DIAGNOSIS, not just plant identification. Focus on health problems, diseases, pests, and care issues. Provide specific, actionable treatment recommendations. Return ONLY the JSON object above, no additional text, explanations, or formatting. Start your response with { and end with }.`;

    const response = await this.makeRequest(imageUri, prompt);

    try {
      // Clean the response by removing any markdown formatting
      let cleanedResponse = response.trim();

      // Remove markdown code blocks if present
      cleanedResponse = cleanedResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');

      // Try to extract JSON from the response if it's wrapped in text
      const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : cleanedResponse;

      let parsed;
      try {
        parsed = JSON.parse(jsonString);
      } catch (parseError) {
        console.error('JSON parse error in diagnosis:', parseError);
        console.error('Raw response:', response);
        console.error('Cleaned response:', cleanedResponse);
        console.error('JSON string:', jsonString);
        throw new Error(`Failed to parse JSON response: ${parseError.message}`);
      }

      // Validate required fields
      if (!parsed.scientificName || !parsed.commonName || !parsed.careInstructions) {
        throw new Error('Missing required fields in response');
      }

      return parsed;
    } catch (error) {
      console.error('Failed to parse OpenRouter diagnosis response:', error);
      console.error('Raw response was:', response);

      // Check if the response indicates it's not a plant
      const responseText = response.toLowerCase();
      const isNotPlant = responseText.includes('not a plant') ||
                        responseText.includes('no plant') ||
                        responseText.includes('not plant') ||
                        responseText.includes('person') ||
                        responseText.includes('animal') ||
                        responseText.includes('object') ||
                        responseText.includes('food');

      if (isNotPlant) {
        // Return a specific "not a plant" response for diagnosis
        return {
          scientificName: 'Not a plant',
          commonName: 'Not a plant',
          description: 'This image does not appear to contain a plant. Please take a photo of a plant for diagnosis.',
          plantType: 'Not applicable',
          nativeRegion: 'Not applicable',
          toxicity: {
            level: 'none',
            warning: 'Not applicable - this is not a plant'
          },
          growthHabit: 'Not applicable',
          growthRate: 'Not applicable',
          matureSize: {
            height: 'Not applicable',
            width: 'Not applicable',
            description: 'Not applicable - this is not a plant'
          },
          bloomTime: 'Not applicable',
          flowerColor: ['Not applicable'],
          foliageType: 'Not applicable',
          hardiness: {
            zones: 'Not applicable',
            minTemperature: 'Not applicable'
          },
          careInstructions: {
            light: 'medium',
            water: 'medium',
            temperature: { min: 18, max: 25, unit: 'C' },
            humidity: 'medium',
            soil: 'Not applicable',
            fertilizer: 'Not applicable',
            toxicity: 'none'
          },
          additionalInfo: {
            pestsAndDiseases: 'Not applicable',
            funFacts: 'This appears to be a non-plant image',
            uses: ['Not applicable'],
            propagation: 'Not applicable',
            seasonalCare: 'Not applicable',
            companionPlants: ['Not applicable'],
            maintenanceLevel: 'Not applicable'
          },
          tags: ['not-a-plant'],
          confidence: 0.95,
          diagnosedProblem: 'Not a plant - cannot diagnose',
          likelyCauses: ['Image does not contain a plant'],
          symptomsObserved: 'No plant symptoms - not a plant image',
          severity: 'Not applicable',
          immediateActions: ['Take a photo of a plant instead'],
          longTermCare: ['Not applicable'],
          productRecommendations: ['Not applicable'],
          stepByStepInstructions: ['1. Take a photo of a plant', '2. Try diagnosis again'],
          preventionTips: ['Ensure you are photographing a plant'],
          prognosis: 'Not applicable - this is not a plant'
        };
      }
      
      // Return a fallback response
      return {
        scientificName: 'Unknown species',
        commonName: 'Unknown plant',
        description: 'Unable to diagnose this plant. Please try again with a clearer image.',
        plantType: 'Unknown',
        nativeRegion: 'Unknown',
        toxicity: {
          level: 'none',
          warning: 'Toxicity information unavailable'
        },
        growthHabit: 'Unknown',
        growthRate: 'Unknown',
        matureSize: {
          height: 'Unknown',
          width: 'Unknown',
          description: 'Size information unavailable'
        },
        bloomTime: 'Unknown',
        flowerColor: ['Unknown'],
        foliageType: 'Unknown',
        hardiness: {
          zones: 'Unknown',
          minTemperature: 'Unknown'
        },
        careInstructions: {
          light: 'medium',
          water: 'medium',
          temperature: { min: 18, max: 25, unit: 'C' },
          humidity: 'medium',
          soil: 'Well-draining potting mix',
          fertilizer: 'Monthly during growing season',
          toxicity: 'none'
        },
        additionalInfo: {
          pestsAndDiseases: 'Information unavailable',
          funFacts: 'Unable to provide information for unidentified plant',
          uses: ['Unknown'],
          propagation: 'Information unavailable',
          seasonalCare: 'Information unavailable',
          companionPlants: ['Unknown'],
          maintenanceLevel: 'Unknown'
        },
        tags: ['unknown'],
        confidence: 0.1,
        diagnosedProblem: 'Unable to diagnose from image',
        likelyCauses: ['Image quality too poor for analysis'],
        symptomsObserved: 'Unable to identify symptoms clearly',
        severity: 'Unknown',
        immediateActions: ['Retake photo with better lighting'],
        longTermCare: ['Consult a plant expert'],
        productRecommendations: ['No recommendations available'],
        stepByStepInstructions: ['1. Take a clearer photo', '2. Try again'],
        preventionTips: ['Ensure good image quality for accurate diagnosis'],
        prognosis: 'Unable to assess without clear diagnosis'
      };
    }
  }
}

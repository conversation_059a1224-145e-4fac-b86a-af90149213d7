/**
 * Error filtering utilities to suppress browser extension errors and other non-application errors
 */

/**
 * Check if an error is from a browser extension
 */
export const isBrowserExtensionError = (error: any): boolean => {
  if (!error) return false;
  
  const errorMessage = error.message?.toLowerCase() || '';
  const errorString = error.toString?.()?.toLowerCase() || '';
  
  const extensionErrorIndicators = [
    'could not establish connection',
    'receiving end does not exist',
    'extension context invalidated',
    'chrome-extension://',
    'moz-extension://',
    'safari-extension://',
    'extension is not available',
    'port closed',
    'message port closed',
    'disconnected port',
  ];
  
  return extensionErrorIndicators.some(indicator => 
    errorMessage.includes(indicator) || errorString.includes(indicator)
  );
};

/**
 * Check if an error should be suppressed from console logging
 */
export const shouldSuppressError = (error: any): boolean => {
  return isBrowserExtensionError(error);
};

/**
 * Filter and log errors, suppressing browser extension errors
 */
export const filteredConsoleError = (...args: any[]) => {
  // Check if any of the arguments contain browser extension errors
  const hasExtensionError = args.some(arg => isBrowserExtensionError(arg));
  
  if (!hasExtensionError) {
    console.error(...args);
  }
};

/**
 * Filter and log warnings, suppressing browser extension warnings
 */
export const filteredConsoleWarn = (...args: any[]) => {
  // Check if any of the arguments contain browser extension errors
  const hasExtensionError = args.some(arg => isBrowserExtensionError(arg));
  
  if (!hasExtensionError) {
    console.warn(...args);
  }
};

/**
 * Setup global error filtering for unhandled promise rejections and errors
 */
export const setupGlobalErrorFiltering = () => {
  // Only run in browser environment
  if (typeof window !== 'undefined') {
    // Filter unhandled promise rejections
    const originalUnhandledRejection = window.onunhandledrejection;
    window.onunhandledrejection = (event) => {
      if (shouldSuppressError(event.reason)) {
        event.preventDefault();
        return;
      }
      
      if (originalUnhandledRejection) {
        originalUnhandledRejection.call(window, event);
      }
    };

    // Filter global errors
    const originalError = window.onerror;
    window.onerror = (message, source, lineno, colno, error) => {
      if (shouldSuppressError(error) || shouldSuppressError({ message })) {
        return true; // Prevent default error handling
      }
      
      if (originalError) {
        return originalError.call(window, message, source, lineno, colno, error);
      }
      
      return false;
    };
  }
};

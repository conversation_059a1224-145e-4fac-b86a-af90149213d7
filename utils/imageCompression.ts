import { Platform } from 'react-native';
import * as ImageManipulator from 'expo-image-manipulator';

export interface CompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number; // 0-1
  format?: 'jpeg' | 'png';
}

export interface CompressionResult {
  uri: string;
  width: number;
  height: number;
  size?: number;
  originalSize?: number;
  compressionRatio?: number;
}

/**
 * Compress an image for optimal storage and processing
 * @param imageUri - The URI of the image to compress
 * @param options - Compression options
 * @returns Compressed image result
 */
export async function compressImage(
  imageUri: string,
  options: CompressionOptions = {}
): Promise<CompressionResult> {
  const {
    maxWidth = 1024,
    maxHeight = 1024,
    quality = 0.8,
    format = 'jpeg'
  } = options;

  try {
    // console.log('Compressing image:', { imageUri, options });

    // Get original image info
    const originalInfo = await ImageManipulator.manipulateAsync(
      imageUri,
      [],
      { format: ImageManipulator.SaveFormat.JPEG }
    );

    // Calculate resize dimensions while maintaining aspect ratio
    const { width: originalWidth, height: originalHeight } = originalInfo;
    let { width: targetWidth, height: targetHeight } = calculateResizeDimensions(
      originalWidth,
      originalHeight,
      maxWidth,
      maxHeight
    );

    // Prepare manipulation actions
    const actions: ImageManipulator.Action[] = [];

    // Add resize action if needed
    if (targetWidth !== originalWidth || targetHeight !== originalHeight) {
      actions.push({
        resize: {
          width: targetWidth,
          height: targetHeight,
        },
      });
    }

    // Determine save format
    const saveFormat = format === 'png' 
      ? ImageManipulator.SaveFormat.PNG 
      : ImageManipulator.SaveFormat.JPEG;

    // Compress the image
    const result = await ImageManipulator.manipulateAsync(
      imageUri,
      actions,
      {
        compress: quality,
        format: saveFormat,
      }
    );

    // Calculate compression stats if possible
    let compressionStats = {};
    if (Platform.OS !== 'web') {
      try {
        // Try to get file sizes for compression ratio calculation
        const originalSize = await getImageSize(imageUri);
        const compressedSize = await getImageSize(result.uri);
        
        if (originalSize && compressedSize) {
          compressionStats = {
            originalSize,
            size: compressedSize,
            compressionRatio: Math.round((1 - compressedSize / originalSize) * 100),
          };
        }
      } catch (error) {
        // console.warn('Could not calculate compression stats:', error);
      }
    }

    // console.log('Image compression complete:', {
    //   original: { width: originalWidth, height: originalHeight },
    //   compressed: { width: result.width, height: result.height },
    //   ...compressionStats,
    // });

    return {
      uri: result.uri,
      width: result.width,
      height: result.height,
      ...compressionStats,
    };
  } catch (error) {
    console.error('Image compression failed:', error);
    // Return original image if compression fails
    return {
      uri: imageUri,
      width: 0,
      height: 0,
    };
  }
}

/**
 * Calculate resize dimensions while maintaining aspect ratio
 */
function calculateResizeDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  const aspectRatio = originalWidth / originalHeight;

  let width = originalWidth;
  let height = originalHeight;

  // Scale down if image is larger than max dimensions
  if (width > maxWidth) {
    width = maxWidth;
    height = width / aspectRatio;
  }

  if (height > maxHeight) {
    height = maxHeight;
    width = height * aspectRatio;
  }

  // Round to integers
  return {
    width: Math.round(width),
    height: Math.round(height),
  };
}

/**
 * Get approximate file size of an image (platform dependent)
 */
async function getImageSize(uri: string): Promise<number | null> {
  try {
    if (Platform.OS === 'web') {
      // For web, try to fetch and get blob size
      const response = await fetch(uri);
      const blob = await response.blob();
      return blob.size;
    } else {
      // For native platforms, we can't easily get file size
      // This would require additional native modules
      return null;
    }
  } catch (error) {
    // console.warn('Could not get image size:', error);
    return null;
  }
}

/**
 * Compress image specifically for LLM processing (smaller size, good quality)
 */
export async function compressForLLM(imageUri: string): Promise<CompressionResult> {
  return compressImage(imageUri, {
    maxWidth: 512,
    maxHeight: 512,
    quality: 0.7,
    format: 'jpeg',
  });
}

/**
 * Compress image for storage (balanced size and quality)
 */
export async function compressForStorage(imageUri: string): Promise<CompressionResult> {
  return compressImage(imageUri, {
    maxWidth: 1024,
    maxHeight: 1024,
    quality: 0.8,
    format: 'jpeg',
  });
}

/**
 * Compress image for avatar use (square, small)
 */
export async function compressForAvatar(imageUri: string): Promise<CompressionResult> {
  return compressImage(imageUri, {
    maxWidth: 256,
    maxHeight: 256,
    quality: 0.8,
    format: 'jpeg',
  });
}

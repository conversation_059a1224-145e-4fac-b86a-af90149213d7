/**
 * Utility to check R2 configuration and provide helpful feedback
 */

import Constants from 'expo-constants';

interface R2ConfigStatus {
  isValid: boolean;
  missingVars: string[];
  warnings: string[];
  recommendations: string[];
}

/**
 * Check R2 configuration and provide detailed feedback
 */
export function checkR2Configuration(): R2ConfigStatus {
  const requiredVars = [
    'CLOUDFLARE_ACCOUNT_ID',
    'R2_ACCESS_KEY_ID', 
    'R2_SECRET_ACCESS_KEY',
    'R2_BUCKET_NAME',
    'R2_ENDPOINT_URL'
  ];

  const config = {
    CLOUDFLARE_ACCOUNT_ID: Constants.expoConfig?.extra?.cloudflareAccountId || process.env.CLOUDFLARE_ACCOUNT_ID,
    R2_ACCESS_KEY_ID: Constants.expoConfig?.extra?.r2AccessKeyId || process.env.R2_ACCESS_KEY_ID,
    R2_SECRET_ACCESS_KEY: Constants.expoConfig?.extra?.r2SecretAccessKey || process.env.R2_SECRET_ACCESS_KEY,
    R2_BUCKET_NAME: Constants.expoConfig?.extra?.r2BucketName || process.env.R2_BUCKET_NAME,
    R2_ENDPOINT_URL: Constants.expoConfig?.extra?.r2EndpointUrl || process.env.R2_ENDPOINT_URL,
  };

  const missingVars: string[] = [];
  const warnings: string[] = [];
  const recommendations: string[] = [];

  // Check for missing variables
  requiredVars.forEach(varName => {
    if (!config[varName as keyof typeof config]) {
      missingVars.push(varName);
    }
  });

  // Validate endpoint URL format
  if (config.R2_ENDPOINT_URL) {
    if (!config.R2_ENDPOINT_URL.startsWith('https://')) {
      warnings.push('R2_ENDPOINT_URL should start with https://');
    }
    if (!config.R2_ENDPOINT_URL.includes('.r2.cloudflarestorage.com')) {
      warnings.push('R2_ENDPOINT_URL should contain .r2.cloudflarestorage.com');
    }
  }

  // Validate account ID in endpoint URL
  if (config.CLOUDFLARE_ACCOUNT_ID && config.R2_ENDPOINT_URL) {
    if (!config.R2_ENDPOINT_URL.includes(config.CLOUDFLARE_ACCOUNT_ID)) {
      warnings.push('R2_ENDPOINT_URL should contain your Cloudflare Account ID');
    }
  }

  // Check access key format
  if (config.R2_ACCESS_KEY_ID && config.R2_ACCESS_KEY_ID.length < 20) {
    warnings.push('R2_ACCESS_KEY_ID seems too short - verify it\'s correct');
  }

  // Check secret key format
  if (config.R2_SECRET_ACCESS_KEY && config.R2_SECRET_ACCESS_KEY.length < 40) {
    warnings.push('R2_SECRET_ACCESS_KEY seems too short - verify it\'s correct');
  }

  // Provide recommendations
  if (missingVars.length > 0) {
    recommendations.push('Add missing environment variables to your .env.local file');
    recommendations.push('Ensure app.config.js includes the R2 variables in the extra section');
  }

  if (warnings.length > 0) {
    recommendations.push('Review and correct the configuration warnings');
  }

  if (missingVars.length === 0 && warnings.length === 0) {
    recommendations.push('Configuration looks good! Test with the R2 storage test script');
  }

  return {
    isValid: missingVars.length === 0,
    missingVars,
    warnings,
    recommendations
  };
}

/**
 * Print R2 configuration status to console
 * COMMENTED OUT FOR PRODUCTION - This is a debug utility
 */
export function printR2ConfigStatus(): void {
  // const status = checkR2Configuration();

  // console.log('\n🔧 Cloudflare R2 Configuration Check\n');

  // if (status.isValid) {
  //   console.log('✅ Configuration is valid!');
  // } else {
  //   console.log('❌ Configuration has issues');
  // }

  // if (status.missingVars.length > 0) {
  //   console.log('\n📋 Missing Environment Variables:');
  //   status.missingVars.forEach(varName => {
  //     console.log(`   - ${varName}`);
  //   });
  // }

  // if (status.warnings.length > 0) {
  //   console.log('\n⚠️  Configuration Warnings:');
  //   status.warnings.forEach(warning => {
  //     console.log(`   - ${warning}`);
  //   });
  // }
}

// Commented out for production
// if (status.recommendations.length > 0) {
//   console.log('\n💡 Recommendations:');
//   status.recommendations.forEach(rec => {
//     console.log(`   - ${rec}`);
//   });
// }

// console.log('\n📖 For detailed setup instructions, see docs/CLOUDFLARE_R2_SETUP.md\n');

/**
 * Get R2 configuration summary for debugging
 */
export function getR2ConfigSummary(): Record<string, any> {
  const config = {
    CLOUDFLARE_ACCOUNT_ID: Constants.expoConfig?.extra?.cloudflareAccountId || process.env.CLOUDFLARE_ACCOUNT_ID,
    R2_ACCESS_KEY_ID: Constants.expoConfig?.extra?.r2AccessKeyId || process.env.R2_ACCESS_KEY_ID,
    R2_SECRET_ACCESS_KEY: Constants.expoConfig?.extra?.r2SecretAccessKey || process.env.R2_SECRET_ACCESS_KEY,
    R2_BUCKET_NAME: Constants.expoConfig?.extra?.r2BucketName || process.env.R2_BUCKET_NAME,
    R2_ENDPOINT_URL: Constants.expoConfig?.extra?.r2EndpointUrl || process.env.R2_ENDPOINT_URL,
  };

  // Mask sensitive values for logging
  return {
    CLOUDFLARE_ACCOUNT_ID: config.CLOUDFLARE_ACCOUNT_ID ? `${config.CLOUDFLARE_ACCOUNT_ID.slice(0, 8)}...` : 'NOT_SET',
    R2_ACCESS_KEY_ID: config.R2_ACCESS_KEY_ID ? `${config.R2_ACCESS_KEY_ID.slice(0, 8)}...` : 'NOT_SET',
    R2_SECRET_ACCESS_KEY: config.R2_SECRET_ACCESS_KEY ? '***HIDDEN***' : 'NOT_SET',
    R2_BUCKET_NAME: config.R2_BUCKET_NAME || 'NOT_SET',
    R2_ENDPOINT_URL: config.R2_ENDPOINT_URL || 'NOT_SET',
  };
}
